#!/usr/bin/env python3
"""
调试训练损失 >= 验证损失的问题
使用最简单的设置来隔离问题
"""

import torch
import torch.nn as nn
import numpy as np
from gnn_model import GCRNPolicy

def create_simple_test_data(num_samples=100):
    """创建简单的测试数据"""
    
    # 固定随机种子确保数据一致
    torch.manual_seed(42)
    np.random.seed(42)
    
    data = []
    for i in range(num_samples):
        # 简单的固定大小数据
        node_features = torch.randn(10, 3)  # 10个节点，3个特征
        adj_matrix = torch.randn(10, 10)    # 邻接矩阵
        expert_actions = torch.randn(10, 2) # 专家动作
        
        data.append((node_features, adj_matrix, expert_actions))
    
    return data

def test_model_consistency(k_value=0):
    """测试模型在训练/验证模式下的一致性"""
    
    print(f"🔍 测试 K={k_value} 模型一致性...")
    
    # 创建模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = GCRNPolicy(K_hops=k_value, hidden_dim=64, dropout=0.0).to(device)
    
    # 创建测试数据
    node_features = torch.randn(10, 3).to(device)
    adj_matrix = torch.randn(10, 10).to(device)
    expert_actions = torch.randn(10, 2).to(device)
    
    criterion = nn.MSELoss()
    
    # 测试1：同一数据在训练/验证模式下的输出
    print("测试1：模型输出一致性")
    
    model.train()
    with torch.no_grad():
        if k_value == 0:
            train_output = model.network(node_features)
        else:
            train_output, _ = model(node_features, adj_matrix, None)
    
    model.eval()
    with torch.no_grad():
        if k_value == 0:
            eval_output = model.network(node_features)
        else:
            eval_output, _ = model(node_features, adj_matrix, None)
    
    output_diff = torch.mean(torch.abs(train_output - eval_output)).item()
    print(f"  输出差异: {output_diff:.8f}")
    
    if output_diff < 1e-6:
        print("  ✅ 模型输出一致")
    else:
        print("  ❌ 模型输出不一致！")
    
    # 测试2：损失计算一致性
    print("测试2：损失计算一致性")
    
    model.train()
    train_loss = criterion(train_output, expert_actions).item()
    
    model.eval()
    eval_loss = criterion(eval_output, expert_actions).item()
    
    print(f"  训练模式损失: {train_loss:.8f}")
    print(f"  验证模式损失: {eval_loss:.8f}")
    print(f"  损失差异: {abs(train_loss - eval_loss):.8f}")
    
    if abs(train_loss - eval_loss) < 1e-6:
        print("  ✅ 损失计算一致")
        return True
    else:
        print("  ❌ 损失计算不一致！")
        return False

def test_training_loop_simple():
    """测试简化的训练循环"""
    
    print(f"\n🔍 测试简化训练循环...")
    
    # 创建数据
    data = create_simple_test_data(50)
    train_data = data[:40]
    val_data = data[40:]
    
    # 创建模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = GCRNPolicy(K_hops=0, hidden_dim=64, dropout=0.0).to(device)
    
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    print("开始简化训练...")
    
    for epoch in range(3):
        # 训练
        model.train()
        total_train_loss = 0
        
        for node_features, adj_matrix, expert_actions in train_data:
            node_features = node_features.to(device)
            adj_matrix = adj_matrix.to(device)
            expert_actions = expert_actions.to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            output = model.network(node_features)
            loss = criterion(output, expert_actions)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            total_train_loss += loss.item()
        
        avg_train_loss = total_train_loss / len(train_data)
        
        # 验证
        model.eval()
        total_val_loss = 0
        
        with torch.no_grad():
            for node_features, adj_matrix, expert_actions in val_data:
                node_features = node_features.to(device)
                adj_matrix = adj_matrix.to(device)
                expert_actions = expert_actions.to(device)
                
                output = model.network(node_features)
                loss = criterion(output, expert_actions)
                
                total_val_loss += loss.item()
        
        avg_val_loss = total_val_loss / len(val_data)
        
        print(f"Epoch {epoch+1}: Train={avg_train_loss:.6f}, Val={avg_val_loss:.6f}")
        
        # 检查异常
        if avg_train_loss >= avg_val_loss:
            print(f"❌ 异常：训练损失({avg_train_loss:.6f}) >= 验证损失({avg_val_loss:.6f})")
            return False
        else:
            print(f"✅ 正常：训练损失({avg_train_loss:.6f}) < 验证损失({avg_val_loss:.6f})")
    
    return True

def main():
    """主函数"""
    
    print("🚀 开始调试训练损失 >= 验证损失问题")
    
    # 测试1：模型一致性
    for k in [0, 1, 2]:
        consistent = test_model_consistency(k)
        if not consistent:
            print(f"❌ K={k}模型不一致，这可能是问题根源")
        else:
            print(f"✅ K={k}模型一致")
    
    # 测试2：简化训练循环
    success = test_training_loop_simple()
    
    if success:
        print("\n🎉 简化测试成功！问题可能在复杂的训练逻辑中")
    else:
        print("\n❌ 简化测试也失败！问题在基础模型或数据中")
    
    print("\n🔧 建议的调试步骤:")
    print("1. 检查是否有隐藏的状态层（如BatchNorm的running stats）")
    print("2. 验证数据加载和预处理逻辑")
    print("3. 检查优化器和学习率调度器")
    print("4. 确认损失函数计算")

if __name__ == "__main__":
    main()
