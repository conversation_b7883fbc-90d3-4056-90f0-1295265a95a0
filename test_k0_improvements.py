#!/usr/bin/env python3
"""
测试K=0改进的脚本
验证K=0时机器人获取空白邻居信息的改进是否正确实现
"""

import torch
import numpy as np
from gnn_model import prepare_node_features, GCRNPolicy
from environment import Environment

def test_k0_adjacency_matrix():
    """测试K=0时邻接矩阵是否只包含自环"""
    print("=" * 60)
    print("测试 K=0 邻接矩阵生成")
    print("=" * 60)
    
    # 创建测试环境
    device = torch.device('cpu')
    env = Environment(width=40.0, height=40.0, n_peaks=3, device=device)
    
    # 创建测试机器人位置
    robot_pos = torch.tensor([
        [10.0, 10.0],
        [15.0, 15.0], 
        [20.0, 20.0],
        [25.0, 25.0],
        [30.0, 30.0]
    ], device=device, dtype=torch.float32)
    
    sensing_radius = 5.0
    
    # 测试K=0
    print("测试 K=0:")
    node_features_k0, adj_matrix_k0 = prepare_node_features(
        robot_pos, env, sensing_radius, k_hops=0
    )
    print(f"邻接矩阵形状: {adj_matrix_k0.shape}")
    print(f"邻接矩阵:\n{adj_matrix_k0}")
    
    # 验证只有对角线为1
    expected_k0 = torch.eye(5, device=device, dtype=torch.float32)
    is_correct_k0 = torch.allclose(adj_matrix_k0, expected_k0)
    print(f"K=0 邻接矩阵是否正确 (只有自环): {is_correct_k0}")
    
    # 测试K=1作为对比
    print("\n测试 K=1 (对比):")
    node_features_k1, adj_matrix_k1 = prepare_node_features(
        robot_pos, env, sensing_radius, k_hops=1
    )
    print(f"邻接矩阵形状: {adj_matrix_k1.shape}")
    print(f"邻接矩阵:\n{adj_matrix_k1}")
    
    # 验证K=1有非零的非对角元素
    off_diagonal_sum = (adj_matrix_k1 - torch.eye(5, device=device)).sum()
    print(f"K=1 非对角元素和: {off_diagonal_sum.item()}")
    print(f"K=1 是否有邻居连接: {off_diagonal_sum > 0}")
    
    return is_correct_k0

def test_model_architecture():
    """测试不同K值的模型架构"""
    print("\n" + "=" * 60)
    print("测试模型架构差异")
    print("=" * 60)
    
    device = torch.device('cpu')
    
    # 创建不同K值的模型
    model_k0 = GCRNPolicy(K_hops=0, hidden_dim=128)
    model_k1 = GCRNPolicy(K_hops=1, hidden_dim=128)
    
    print(f"K=0 模型参数数量: {sum(p.numel() for p in model_k0.parameters())}")
    print(f"K=1 模型参数数量: {sum(p.numel() for p in model_k1.parameters())}")
    
    # 检查GCRN层类型
    print(f"K=0 GCRN层类型: {type(model_k0.gcrn).__name__}")
    print(f"K=1 GCRN层类型: {type(model_k1.gcrn).__name__}")
    
    # 测试前向传播
    n_robots = 5
    node_features = torch.randn(n_robots, 3)
    adj_matrix_k0 = torch.eye(n_robots)
    adj_matrix_k1 = torch.rand(n_robots, n_robots)
    adj_matrix_k1 = adj_matrix_k1 + adj_matrix_k1.T + torch.eye(n_robots)  # 对称化
    
    # K=0前向传播
    model_k0.reset(n_robots, device)
    output_k0, hidden_k0 = model_k0(node_features, adj_matrix_k0)
    print(f"K=0 输出形状: {output_k0.shape}")
    
    # K=1前向传播
    model_k1.reset(n_robots, device)
    output_k1, hidden_k1 = model_k1(node_features, adj_matrix_k1)
    print(f"K=1 输出形状: {output_k1.shape}")
    
    return True

def test_feature_consistency():
    """测试特征一致性"""
    print("\n" + "=" * 60)
    print("测试特征一致性")
    print("=" * 60)
    
    device = torch.device('cpu')
    env = Environment(width=40.0, height=40.0, n_peaks=3, device=device)
    
    robot_pos = torch.tensor([
        [10.0, 10.0],
        [15.0, 15.0], 
        [20.0, 20.0]
    ], device=device, dtype=torch.float32)
    
    sensing_radius = 5.0
    
    # 获取K=0和K=1的特征
    node_features_k0, _ = prepare_node_features(robot_pos, env, sensing_radius, k_hops=0)
    node_features_k1, _ = prepare_node_features(robot_pos, env, sensing_radius, k_hops=1)
    
    print(f"K=0 节点特征形状: {node_features_k0.shape}")
    print(f"K=1 节点特征形状: {node_features_k1.shape}")
    
    # 特征应该相同（因为都是基于相同的Voronoi计算）
    features_equal = torch.allclose(node_features_k0, node_features_k1, atol=1e-6)
    print(f"K=0和K=1的节点特征是否相同: {features_equal}")
    
    if not features_equal:
        print("特征差异:")
        print(f"最大差异: {torch.max(torch.abs(node_features_k0 - node_features_k1))}")
    
    return features_equal

def main():
    """主测试函数"""
    print("开始测试K=0改进...")
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 运行测试
    test1_passed = test_k0_adjacency_matrix()
    test2_passed = test_model_architecture()
    test3_passed = test_feature_consistency()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"K=0 邻接矩阵测试: {'通过' if test1_passed else '失败'}")
    print(f"模型架构测试: {'通过' if test2_passed else '失败'}")
    print(f"特征一致性测试: {'通过' if test3_passed else '失败'}")
    
    all_passed = test1_passed and test2_passed and test3_passed
    print(f"\n总体测试结果: {'全部通过' if all_passed else '存在问题'}")
    
    if all_passed:
        print("\n✅ K=0改进已正确实现！")
        print("现在K=0模型将只接收自环邻接矩阵，确保没有邻居信息泄露。")
    else:
        print("\n❌ 存在问题需要修复。")

if __name__ == "__main__":
    main()
