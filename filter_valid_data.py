#!/usr/bin/env python3
"""
过滤有效数据，实现数据量 K=2 > K=1 > K=0
只保留有效动作（非零动作），去除零动作
"""

import torch
import os
import glob
import numpy as np
from tqdm import tqdm

def analyze_data_distribution():
    """分析数据分布，确定过滤策略"""
    
    print("🔍 分析数据分布...")
    
    stats = {}
    
    for k in [0, 1, 2]:
        k_dir = os.path.join('training_dataset_processed', f'k{k}')
        file_paths = sorted(glob.glob(os.path.join(k_dir, 'episode_*.pth')))
        
        total_actions = 0
        valid_actions = 0
        zero_actions = 0
        
        print(f"\nK={k}: 分析{len(file_paths)}个文件...")
        
        for f_path in tqdm(file_paths[:100], desc=f"Analyzing K={k}"):  # 分析前100个文件
            try:
                data = torch.load(f_path, map_location='cpu', weights_only=False)
                expert_actions = data['expert_actions']  # [seq_len, num_nodes, 2]
                
                # 计算动作幅度
                action_norms = torch.norm(expert_actions, dim=-1)  # [seq_len, num_nodes]
                
                # 统计
                file_total = action_norms.numel()
                file_valid = (action_norms > 1e-6).sum().item()
                file_zero = file_total - file_valid
                
                total_actions += file_total
                valid_actions += file_valid
                zero_actions += file_zero
                
            except Exception as e:
                print(f"分析失败 {f_path}: {e}")
                continue
        
        valid_ratio = valid_actions / total_actions if total_actions > 0 else 0
        
        stats[k] = {
            'total': total_actions,
            'valid': valid_actions,
            'zero': zero_actions,
            'valid_ratio': valid_ratio
        }
        
        print(f"K={k} 统计:")
        print(f"  总动作: {total_actions:,}")
        print(f"  有效动作: {valid_actions:,} ({valid_ratio*100:.1f}%)")
        print(f"  零动作: {zero_actions:,} ({(1-valid_ratio)*100:.1f}%)")
    
    return stats

def create_filtered_dataset(k_value, target_valid_samples):
    """创建过滤后的数据集，只保留有效动作"""
    
    print(f"\n🔧 为K={k_value}创建过滤数据集，目标有效样本数: {target_valid_samples:,}")
    
    k_dir = os.path.join('training_dataset_processed', f'k{k_value}')
    file_paths = sorted(glob.glob(os.path.join(k_dir, 'episode_*.pth')))
    
    filtered_data = []
    valid_count = 0
    
    for f_path in tqdm(file_paths, desc=f"Filtering K={k_value}"):
        if valid_count >= target_valid_samples:
            break
            
        try:
            data = torch.load(f_path, map_location='cpu', weights_only=False)
            node_features = data['node_features']  # [seq_len, num_nodes, 3]
            adj_matrices = data['adj_matrices']    # [seq_len, num_nodes, num_nodes]
            expert_actions = data['expert_actions'] # [seq_len, num_nodes, 2]
            
            # 计算动作幅度
            action_norms = torch.norm(expert_actions, dim=-1)  # [seq_len, num_nodes]
            
            # 找到有效时间步和节点
            valid_mask = action_norms > 1e-6  # [seq_len, num_nodes]
            
            # 提取有效的时间步
            for t in range(node_features.shape[0]):
                if valid_count >= target_valid_samples:
                    break
                    
                # 检查这个时间步是否有有效动作
                if valid_mask[t].any():
                    # 保存这个时间步的数据
                    filtered_data.append({
                        'node_features': node_features[t],      # [num_nodes, 3]
                        'adj_matrix': adj_matrices[t],          # [num_nodes, num_nodes]
                        'expert_actions': expert_actions[t],    # [num_nodes, 2]
                        'valid_mask': valid_mask[t]             # [num_nodes]
                    })
                    valid_count += 1
            
        except Exception as e:
            print(f"处理失败 {f_path}: {e}")
            continue
    
    print(f"K={k_value}: 收集到{len(filtered_data)}个有效样本")
    return filtered_data

def save_filtered_dataset(k_value, filtered_data):
    """保存过滤后的数据集"""
    
    output_dir = f'filtered_dataset_k{k_value}'
    os.makedirs(output_dir, exist_ok=True)
    
    # 将数据分批保存
    batch_size = 1000
    num_batches = (len(filtered_data) + batch_size - 1) // batch_size
    
    for batch_idx in range(num_batches):
        start_idx = batch_idx * batch_size
        end_idx = min((batch_idx + 1) * batch_size, len(filtered_data))
        batch_data = filtered_data[start_idx:end_idx]
        
        # 转换为序列格式（为了兼容现有的训练代码）
        node_features_list = []
        adj_matrices_list = []
        expert_actions_list = []
        
        for item in batch_data:
            node_features_list.append(item['node_features'])
            adj_matrices_list.append(item['adj_matrix'])
            expert_actions_list.append(item['expert_actions'])
        
        # 堆叠成序列
        batch_node_features = torch.stack(node_features_list)    # [batch_size, num_nodes, 3]
        batch_adj_matrices = torch.stack(adj_matrices_list)      # [batch_size, num_nodes, num_nodes]
        batch_expert_actions = torch.stack(expert_actions_list)  # [batch_size, num_nodes, 2]
        
        batch_file = os.path.join(output_dir, f'batch_{batch_idx:04d}.pth')
        torch.save({
            'node_features': batch_node_features,
            'adj_matrices': batch_adj_matrices,
            'expert_actions': batch_expert_actions
        }, batch_file)
    
    print(f"K={k_value}: 保存了{num_batches}个批次到 {output_dir}/")

def main():
    """主函数：创建平衡的过滤数据集"""
    
    print("🚀 开始创建过滤数据集")
    print("目标：数据量 K=2 > K=1 > K=0，只保留有效动作")
    
    # 1. 分析数据分布
    stats = analyze_data_distribution()
    
    # 2. 确定目标样本数量
    # 基于K=0的有效动作数量来设定基准
    k0_valid = stats[0]['valid']
    
    target_samples = {
        0: k0_valid,                    # K=0: 基准数量
        1: int(k0_valid * 1.5),        # K=1: 1.5倍
        2: int(k0_valid * 2.0)         # K=2: 2倍
    }
    
    print(f"\n🎯 目标样本数量:")
    for k in [0, 1, 2]:
        print(f"  K={k}: {target_samples[k]:,} 个有效样本")
    
    # 3. 创建过滤数据集
    for k in [0, 1, 2]:
        print(f"\n🔧 处理 K={k}...")
        filtered_data = create_filtered_dataset(k, target_samples[k])
        save_filtered_dataset(k, filtered_data)
    
    print(f"\n✅ 过滤数据集创建完成！")
    print("新的数据集目录:")
    print("  - filtered_dataset_k0/")
    print("  - filtered_dataset_k1/")
    print("  - filtered_dataset_k2/")

if __name__ == "__main__":
    main()
