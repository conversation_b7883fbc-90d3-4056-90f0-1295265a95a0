#!/usr/bin/env python3
"""
改进方案：时间步长和数据增强策略
"""

import torch
import numpy as np
import argparse
from typing import List, Tuple, Dict, Any

# ============================================================================
# 1. 动态时间步长处理
# ============================================================================

class DynamicTimeStep:
    """动态时间步长处理器"""
    
    def __init__(self, base_dt: float = 0.05, adaptive: bool = True):
        """
        Args:
            base_dt: 基础时间步长
            adaptive: 是否启用自适应时间步长
        """
        self.base_dt = base_dt
        self.adaptive = adaptive
        
    def compute_adaptive_dt(self, velocities: torch.Tensor, max_velocity: float = 1.0) -> float:
        """
        基于机器人速度自适应计算时间步长
        
        Args:
            velocities: 机器人速度 [N, 2]
            max_velocity: 最大允许速度
            
        Returns:
            自适应时间步长
        """
        if not self.adaptive:
            return self.base_dt
            
        # 计算最大速度
        max_vel = torch.max(torch.norm(velocities, dim=1))
        
        if max_vel > max_velocity:
            # 如果速度过大，减小时间步长
            adaptive_dt = self.base_dt * (max_velocity / max_vel.item())
        else:
            # 否则使用基础时间步长
            adaptive_dt = self.base_dt
            
        # 限制时间步长范围
        adaptive_dt = max(0.01, min(0.1, adaptive_dt))
        
        return adaptive_dt
    
    def process_expert_actions(self, trajectory: torch.Tensor, dt: float = None) -> torch.Tensor:
        """
        处理专家轨迹，计算动作（速度）
        
        Args:
            trajectory: 轨迹 [T, N, 2]
            dt: 时间步长，如果为None则使用base_dt
            
        Returns:
            专家动作 [T-1, N, 2]
        """
        if dt is None:
            dt = self.base_dt
            
        # 计算位置差分
        position_diff = trajectory[1:] - trajectory[:-1]
        
        # 转换为速度
        expert_actions = position_diff / dt
        
        return expert_actions

# ============================================================================
# 2. 增强的数据增强策略
# ============================================================================

class AdvancedDataAugmentation:
    """高级数据增强策略"""
    
    def __init__(self, 
                 rotation_prob: float = 0.5,
                 scaling_prob: float = 0.3,
                 noise_prob: float = 0.4,
                 temporal_prob: float = 0.2):
        """
        Args:
            rotation_prob: 旋转增强概率
            scaling_prob: 缩放增强概率  
            noise_prob: 噪声增强概率
            temporal_prob: 时间增强概率
        """
        self.rotation_prob = rotation_prob
        self.scaling_prob = scaling_prob
        self.noise_prob = noise_prob
        self.temporal_prob = temporal_prob
        
    def random_rotation(self, positions: torch.Tensor, actions: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """随机旋转增强"""
        if np.random.rand() > self.rotation_prob:
            return positions, actions
            
        # 随机旋转角度
        theta = np.random.uniform(0, 2*np.pi)
        cos_theta, sin_theta = np.cos(theta), np.sin(theta)
        
        # 旋转矩阵
        rotation_matrix = torch.tensor([
            [cos_theta, -sin_theta],
            [sin_theta, cos_theta]
        ], dtype=positions.dtype, device=positions.device)
        
        # 应用旋转
        rotated_positions = torch.matmul(positions, rotation_matrix.T)
        rotated_actions = torch.matmul(actions, rotation_matrix.T)
        
        return rotated_positions, rotated_actions
    
    def random_scaling(self, positions: torch.Tensor, actions: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """随机缩放增强"""
        if np.random.rand() > self.scaling_prob:
            return positions, actions
            
        # 随机缩放因子 (0.8 到 1.2)
        scale_factor = np.random.uniform(0.8, 1.2)
        
        # 应用缩放
        scaled_positions = positions * scale_factor
        scaled_actions = actions * scale_factor
        
        return scaled_positions, scaled_actions
    
    def add_noise(self, positions: torch.Tensor, actions: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """添加噪声增强"""
        if np.random.rand() > self.noise_prob:
            return positions, actions
            
        # 位置噪声 (标准差为位置范围的1%)
        pos_std = torch.std(positions) * 0.01
        pos_noise = torch.randn_like(positions) * pos_std
        
        # 动作噪声 (标准差为动作范围的2%)
        action_std = torch.std(actions) * 0.02
        action_noise = torch.randn_like(actions) * action_std
        
        noisy_positions = positions + pos_noise
        noisy_actions = actions + action_noise
        
        return noisy_positions, noisy_actions
    
    def temporal_augmentation(self, sequence: torch.Tensor) -> torch.Tensor:
        """时间序列增强：随机采样子序列"""
        if np.random.rand() > self.temporal_prob:
            return sequence
            
        seq_len = sequence.shape[0]
        if seq_len <= 10:
            return sequence
            
        # 随机选择子序列长度 (80%-100%的原长度)
        min_len = max(10, int(seq_len * 0.8))
        new_len = np.random.randint(min_len, seq_len + 1)
        
        # 随机选择起始位置
        start_idx = np.random.randint(0, seq_len - new_len + 1)
        
        return sequence[start_idx:start_idx + new_len]
    
    def apply_augmentation(self, 
                          node_features_seq: List[torch.Tensor],
                          expert_actions: torch.Tensor) -> Tuple[List[torch.Tensor], torch.Tensor]:
        """
        应用所有数据增强策略
        
        Args:
            node_features_seq: 节点特征序列
            expert_actions: 专家动作
            
        Returns:
            增强后的特征序列和动作
        """
        # 提取位置信息（假设前两维是位置差异）
        positions_seq = torch.stack([features[:, :2] for features in node_features_seq])
        
        # 1. 时间增强
        augmented_positions = self.temporal_augmentation(positions_seq)
        augmented_actions = expert_actions[:augmented_positions.shape[0]]
        
        # 2. 空间增强
        for t in range(augmented_positions.shape[0]):
            pos_t = augmented_positions[t]
            action_t = augmented_actions[t]
            
            # 旋转
            pos_t, action_t = self.random_rotation(pos_t, action_t)
            
            # 缩放
            pos_t, action_t = self.random_scaling(pos_t, action_t)
            
            # 噪声
            pos_t, action_t = self.add_noise(pos_t, action_t)
            
            augmented_positions[t] = pos_t
            augmented_actions[t] = action_t
        
        # 重构特征序列
        augmented_features_seq = []
        for t in range(augmented_positions.shape[0]):
            # 保持原始特征的其他维度不变
            original_features = node_features_seq[t].clone()
            original_features[:, :2] = augmented_positions[t]
            augmented_features_seq.append(original_features)
        
        return augmented_features_seq, augmented_actions

# ============================================================================
# 3. 配置管理
# ============================================================================

class TrainingConfig:
    """训练配置管理"""
    
    def __init__(self):
        # 时间步长配置
        self.base_dt = 0.05
        self.adaptive_dt = True
        self.max_velocity = 1.0
        
        # 数据增强配置
        self.augmentation_enabled = True
        self.rotation_prob = 0.5
        self.scaling_prob = 0.3
        self.noise_prob = 0.4
        self.temporal_prob = 0.2
        
        # K-hop配置
        self.k_hops_list = [0, 1, 2]
        self.sensing_radius = 5.0
        
    @classmethod
    def from_args(cls, args: argparse.Namespace) -> 'TrainingConfig':
        """从命令行参数创建配置"""
        config = cls()
        
        # 更新配置
        if hasattr(args, 'base_dt'):
            config.base_dt = args.base_dt
        if hasattr(args, 'adaptive_dt'):
            config.adaptive_dt = args.adaptive_dt
        if hasattr(args, 'augmentation_enabled'):
            config.augmentation_enabled = args.augmentation_enabled
        if hasattr(args, 'k_hops_to_train'):
            config.k_hops_list = args.k_hops_to_train
        if hasattr(args, 'sensing_radius'):
            config.sensing_radius = args.sensing_radius
            
        return config
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'base_dt': self.base_dt,
            'adaptive_dt': self.adaptive_dt,
            'max_velocity': self.max_velocity,
            'augmentation_enabled': self.augmentation_enabled,
            'rotation_prob': self.rotation_prob,
            'scaling_prob': self.scaling_prob,
            'noise_prob': self.noise_prob,
            'temporal_prob': self.temporal_prob,
            'k_hops_list': self.k_hops_list,
            'sensing_radius': self.sensing_radius
        }

# ============================================================================
# 4. 使用示例
# ============================================================================

def example_usage():
    """使用示例"""
    
    # 创建配置
    config = TrainingConfig()
    
    # 创建时间步长处理器
    time_processor = DynamicTimeStep(
        base_dt=config.base_dt,
        adaptive=config.adaptive_dt
    )
    
    # 创建数据增强器
    augmenter = AdvancedDataAugmentation(
        rotation_prob=config.rotation_prob,
        scaling_prob=config.scaling_prob,
        noise_prob=config.noise_prob,
        temporal_prob=config.temporal_prob
    )
    
    print("改进方案配置:")
    print(f"  基础时间步长: {config.base_dt}")
    print(f"  自适应时间步长: {config.adaptive_dt}")
    print(f"  数据增强启用: {config.augmentation_enabled}")
    print(f"  K值列表: {config.k_hops_list}")
    print(f"  感知半径: {config.sensing_radius}")

if __name__ == "__main__":
    example_usage()
