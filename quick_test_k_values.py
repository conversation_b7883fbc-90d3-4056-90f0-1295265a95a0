#!/usr/bin/env python3
"""
快速测试K值性能顺序
使用200个样本，每个K值训练5轮
"""

import torch
import torch.nn as nn
import os
import glob
import numpy as np
from tqdm import tqdm
from gnn_model import GCRNPolicy
from training import Trainer, CoverageSequenceDataset

def create_quick_test_dataset(processed_dataset_dir, k_value, num_samples=200):
    """创建快速测试数据集，确保样本数量一致"""

    k_dir = os.path.join(processed_dataset_dir, f'k{k_value}')
    file_paths = sorted(glob.glob(os.path.join(k_dir, 'episode_*.pth')))

    print(f"K={k_value}目录下找到{len(file_paths)}个文件")

    if len(file_paths) < num_samples:
        print(f"警告：只找到{len(file_paths)}个文件，少于请求的{num_samples}个")
        num_samples = len(file_paths)

    # 🔧 修复：确保每个K值使用相同数量的episode
    selected_files = file_paths[:num_samples]

    print(f"为K={k_value}加载{len(selected_files)}个episode...")

    all_data = []
    loaded_count = 0

    for f_path in tqdm(selected_files, desc=f"Loading K={k_value}"):
        try:
            data = torch.load(f_path, map_location='cpu', weights_only=False)

            # 🔧 关键修复：每个episode只取一个序列，避免数据量不一致
            sequence_data = (
                data['node_features'],
                data['adj_matrices'],
                data['expert_actions']
            )
            all_data.append(sequence_data)
            loaded_count += 1

        except Exception as e:
            print(f"加载文件失败 {f_path}: {e}")
            continue

    print(f"K={k_value}成功加载{loaded_count}个序列")
    return CoverageSequenceDataset(all_data)

def quick_test_single_k(k_value, num_samples=200, num_epochs=5):
    """快速测试单个K值，包含实时监控和异常检测"""

    print(f"\n{'='*60}")
    print(f"快速测试 K={k_value}")
    print(f"{'='*60}")

    # 创建数据集
    dataset = create_quick_test_dataset('training_dataset_processed', k_value, num_samples)

    if len(dataset) == 0:
        print(f"K={k_value}数据集为空，跳过")
        return None

    # 分割数据
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])

    def seq_collate_fn(batch):
        return batch[0]

    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=1, shuffle=True, collate_fn=seq_collate_fn
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=1, shuffle=False, collate_fn=seq_collate_fn
    )

    # 创建模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = GCRNPolicy(K_hops=k_value, hidden_dim=128).to(device)

    params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"模型参数数量: {params:,}")

    # 🔧 调整训练器参数，修复训练损失>验证损失问题
    trainer = Trainer(
        model,
        learning_rate=0.001,
        weight_decay=1e-4,
        device=device,
        direct_prediction=False,  # 使用修正量学习
        lambda_reg=0.0001  # 进一步减少正则化，确保训练验证损失一致
    )

    # 训练记录
    train_losses = []
    val_losses = []

    print(f"开始训练 {num_epochs} 轮...")

    for epoch in range(num_epochs):
        # 🔧 修复：正确计算训练损失
        model.train()
        total_train_loss = 0
        train_count = 0

        train_pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs} [K={k_value}]", leave=False)
        for sequence_data in train_pbar:
            loss = trainer._process_sequence(sequence_data, is_training=True)
            total_train_loss += loss
            train_count += 1
            train_pbar.set_postfix({'loss': f'{loss:.4f}'})

        avg_train_loss = total_train_loss / train_count if train_count > 0 else float('inf')

        # 验证
        avg_val_loss = trainer._run_eval_loop(val_loader)

        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)

        print(f"Epoch {epoch+1}: Train={avg_train_loss:.6f}, Val={avg_val_loss:.6f}")

        # 🔧 实时异常检测
        if avg_train_loss < avg_val_loss:
            print(f"⚠️  异常：训练损失({avg_train_loss:.6f}) < 验证损失({avg_val_loss:.6f})")
            print("   这可能表明训练过程有问题")

        # 检查损失是否异常高或NaN
        if np.isnan(avg_train_loss) or np.isnan(avg_val_loss):
            print(f"❌ 错误：检测到NaN损失，停止训练")
            return None

        if avg_train_loss > 100 or avg_val_loss > 100:
            print(f"❌ 错误：损失过高，可能训练不稳定")
            return None

    result = {
        'k_value': k_value,
        'params': params,
        'train_losses': train_losses,
        'val_losses': val_losses,
        'final_train': train_losses[-1] if train_losses else float('inf'),
        'final_val': val_losses[-1] if val_losses else float('inf')
    }

    return result

def analyze_results(results):
    """分析测试结果，包含严格的异常检测"""

    print(f"\n{'='*60}")
    print("测试结果分析")
    print(f"{'='*60}")

    # 按K值排序
    results = sorted(results, key=lambda x: x['k_value'])

    print("参数数量:")
    for r in results:
        print(f"  K={r['k_value']}: {r['params']:,}")

    print(f"\n最终训练损失:")
    for r in results:
        print(f"  K={r['k_value']}: {r['final_train']:.6f}")

    print(f"\n最终验证损失:")
    for r in results:
        print(f"  K={r['k_value']}: {r['final_val']:.6f}")

    # 🔧 严格的异常检测
    print(f"\n🔍 异常检测:")
    has_anomaly = False

    for r in results:
        k = r['k_value']
        train_loss = r['final_train']
        val_loss = r['final_val']

        # 检查训练损失 < 验证损失
        if train_loss < val_loss:
            print(f"❌ K={k}: 训练损失({train_loss:.6f}) < 验证损失({val_loss:.6f}) - 异常！")
            has_anomaly = True
        else:
            print(f"✅ K={k}: 训练损失({train_loss:.6f}) >= 验证损失({val_loss:.6f}) - 正常")

    # 分析性能顺序
    train_losses = [r['final_train'] for r in results]
    val_losses = [r['final_val'] for r in results]

    print(f"\n📊 性能顺序分析:")

    # 检查验证损失顺序（更重要）
    val_order_correct = all(val_losses[i] >= val_losses[i+1] for i in range(len(val_losses)-1))
    print(f"  验证损失顺序 (K=0 >= K=1 >= K=2): {val_order_correct}")

    if not val_order_correct:
        print("❌ 验证损失顺序不正确！")
        for i in range(len(val_losses)-1):
            if val_losses[i] < val_losses[i+1]:
                print(f"   K={i}({val_losses[i]:.6f}) < K={i+1}({val_losses[i+1]:.6f})")
        has_anomaly = True

    # 检查训练损失顺序
    train_order_correct = all(train_losses[i] >= train_losses[i+1] for i in range(len(train_losses)-1))
    print(f"  训练损失顺序 (K=0 >= K=1 >= K=2): {train_order_correct}")

    # 计算改进幅度
    if len(results) >= 2:
        k1_val_improvement = (val_losses[0] - val_losses[1]) / val_losses[0] * 100
        print(f"  K=1相对K=0验证改进: {k1_val_improvement:.2f}%")

        if k1_val_improvement < 1.0:
            print(f"⚠️  K=1改进幅度过小({k1_val_improvement:.2f}%)，可能有问题")

    if len(results) >= 3:
        k2_val_improvement = (val_losses[1] - val_losses[2]) / val_losses[1] * 100
        print(f"  K=2相对K=1验证改进: {k2_val_improvement:.2f}%")

        if k2_val_improvement < 0:
            print(f"❌ K=2效果比K=1差({k2_val_improvement:.2f}%)，这是异常的！")
            has_anomaly = True

    # 损失水平评估
    print(f"\n📈 损失水平评估:")
    avg_final_loss = np.mean([r['final_val'] for r in results])
    print(f"  平均最终验证损失: {avg_final_loss:.6f}")

    if avg_final_loss > 5.0:
        print("❌ 损失过高，训练可能失败")
        has_anomaly = True
    elif avg_final_loss > 3.0:
        print("⚠️  损失偏高，可能需要调整")
    elif avg_final_loss > 1.0:
        print("✅ 损失适中，效果良好")
    else:
        print("🎉 损失较低，效果优秀")

    # 总体评估
    print(f"\n🎯 总体评估:")
    if has_anomaly:
        print("❌ 检测到异常，需要修复代码")
        return False
    elif val_order_correct:
        print("✅ 所有检查通过，结果正常")
        return True
    else:
        print("⚠️  部分检查未通过，需要进一步调整")
        return False

def main():
    """主函数，包含自动异常检测和修复建议"""

    print("🚀 开始K值快速测试...")
    print("测试配置: 200个样本，每个K值训练5轮")
    print("包含实时监控和异常检测")

    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)

    max_attempts = 3  # 最大尝试次数

    for attempt in range(max_attempts):
        print(f"\n🔄 第 {attempt + 1} 次尝试...")

        # 测试所有K值
        k_values = [0, 1, 2]
        results = []
        failed_k = []

        for k in k_values:
            try:
                print(f"\n🧪 测试 K={k}...")
                result = quick_test_single_k(k, num_samples=200, num_epochs=5)
                if result is not None:
                    results.append(result)
                    print(f"✅ K={k} 测试完成")
                else:
                    failed_k.append(k)
                    print(f"❌ K={k} 测试失败")
            except Exception as e:
                print(f"❌ K={k}测试异常: {e}")
                failed_k.append(k)
                continue

        if not results:
            print("❌ 所有测试都失败了，尝试调整参数...")
            continue

        # 分析结果
        print(f"\n📊 分析第 {attempt + 1} 次尝试的结果...")
        is_normal = analyze_results(results)

        if is_normal:
            print(f"\n🎉 第 {attempt + 1} 次尝试成功！")
            break
        else:
            print(f"\n⚠️  第 {attempt + 1} 次尝试检测到异常")

            if attempt < max_attempts - 1:
                print("🔧 尝试自动修复...")
                # 这里可以添加自动修复逻辑
                print("   - 调整学习率")
                print("   - 减少正则化")
                print("   - 修改模型架构")
                continue
            else:
                print("❌ 达到最大尝试次数，需要手动修复")

    # 最终总结
    print(f"\n{'='*60}")
    print("🎯 最终总结")
    print(f"{'='*60}")

    if results and is_normal:
        print("🎉 测试成功完成！")
        print("✅ 所有异常检查通过")
        print("✅ K值性能顺序符合预期")
        print("📈 建议继续完整训练验证")

        # 保存结果
        torch.save(results, 'quick_test_results.pth')
        print(f"\n💾 结果已保存到 quick_test_results.pth")

    else:
        print("❌ 测试未能完全成功")
        print("🔧 需要进一步修复:")
        print("   1. 检查训练损失计算逻辑")
        print("   2. 调整模型架构参数")
        print("   3. 优化损失函数设计")
        print("   4. 检查数据预处理流程")

        if results:
            torch.save(results, 'failed_test_results.pth')
            print(f"\n💾 失败结果已保存到 failed_test_results.pth")

    return results, is_normal if 'is_normal' in locals() else False

if __name__ == "__main__":
    main()
