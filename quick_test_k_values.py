#!/usr/bin/env python3
"""
快速测试K值性能顺序
使用200个样本，每个K值训练5轮
"""

import torch
import torch.nn as nn
import os
import glob
import numpy as np
from tqdm import tqdm
from gnn_model import GCRNPolicy
from training import Trainer, CoverageSequenceDataset

def create_quick_test_dataset(processed_dataset_dir, k_value, num_samples=200):
    """创建快速测试数据集"""
    
    k_dir = os.path.join(processed_dataset_dir, f'k{k_value}')
    file_paths = sorted(glob.glob(os.path.join(k_dir, 'episode_*.pth')))
    
    if len(file_paths) < num_samples:
        print(f"警告：只找到{len(file_paths)}个文件，少于请求的{num_samples}个")
        num_samples = len(file_paths)
    
    # 选择前num_samples个文件
    selected_files = file_paths[:num_samples]
    
    print(f"为K={k_value}加载{len(selected_files)}个样本...")
    
    all_data = []
    for f_path in tqdm(selected_files, desc=f"Loading K={k_value}"):
        try:
            data = torch.load(f_path, map_location='cpu', weights_only=False)
            all_data.append((
                data['node_features'],
                data['adj_matrices'], 
                data['expert_actions']
            ))
        except Exception as e:
            print(f"加载文件失败 {f_path}: {e}")
            continue
    
    return CoverageSequenceDataset(all_data)

def quick_test_single_k(k_value, num_samples=200, num_epochs=5):
    """快速测试单个K值"""
    
    print(f"\n{'='*60}")
    print(f"快速测试 K={k_value}")
    print(f"{'='*60}")
    
    # 创建数据集
    dataset = create_quick_test_dataset('training_dataset_processed', k_value, num_samples)
    
    if len(dataset) == 0:
        print(f"K={k_value}数据集为空，跳过")
        return None
    
    # 分割数据
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    def seq_collate_fn(batch):
        return batch[0]
    
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=1, shuffle=True, collate_fn=seq_collate_fn
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=1, shuffle=False, collate_fn=seq_collate_fn
    )
    
    # 创建模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = GCRNPolicy(K_hops=k_value, hidden_dim=128).to(device)
    
    params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"模型参数数量: {params:,}")
    
    # 创建训练器
    trainer = Trainer(
        model, 
        learning_rate=0.001, 
        weight_decay=1e-4, 
        device=device, 
        direct_prediction=False  # 使用修正量学习
    )
    
    # 训练记录
    train_losses = []
    val_losses = []
    
    print(f"开始训练 {num_epochs} 轮...")
    
    for epoch in range(num_epochs):
        # 训练
        model.train()
        total_train_loss = 0
        train_count = 0
        
        train_pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs} [K={k_value}]", leave=False)
        for sequence_data in train_pbar:
            loss = trainer._process_sequence(sequence_data, is_training=True)
            total_train_loss += loss
            train_count += 1
            train_pbar.set_postfix({'loss': f'{loss:.4f}'})
        
        avg_train_loss = total_train_loss / train_count if train_count > 0 else float('inf')
        
        # 验证
        avg_val_loss = trainer._run_eval_loop(val_loader)
        
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        
        print(f"Epoch {epoch+1}: Train={avg_train_loss:.6f}, Val={avg_val_loss:.6f}")
    
    result = {
        'k_value': k_value,
        'params': params,
        'train_losses': train_losses,
        'val_losses': val_losses,
        'final_train': train_losses[-1] if train_losses else float('inf'),
        'final_val': val_losses[-1] if val_losses else float('inf')
    }
    
    return result

def analyze_results(results):
    """分析测试结果"""
    
    print(f"\n{'='*60}")
    print("测试结果分析")
    print(f"{'='*60}")
    
    # 按K值排序
    results = sorted(results, key=lambda x: x['k_value'])
    
    print("参数数量:")
    for r in results:
        print(f"  K={r['k_value']}: {r['params']:,}")
    
    print(f"\n最终训练损失:")
    for r in results:
        print(f"  K={r['k_value']}: {r['final_train']:.6f}")
    
    print(f"\n最终验证损失:")
    for r in results:
        print(f"  K={r['k_value']}: {r['final_val']:.6f}")
    
    # 分析性能顺序
    train_losses = [r['final_train'] for r in results]
    val_losses = [r['final_val'] for r in results]
    
    print(f"\n性能分析:")
    
    # 检查训练损失顺序
    train_order_correct = all(train_losses[i] >= train_losses[i+1] for i in range(len(train_losses)-1))
    print(f"  训练损失顺序 (K=0 >= K=1 >= K=2): {train_order_correct}")
    
    # 检查验证损失顺序
    val_order_correct = all(val_losses[i] >= val_losses[i+1] for i in range(len(val_losses)-1))
    print(f"  验证损失顺序 (K=0 >= K=1 >= K=2): {val_order_correct}")
    
    # 计算改进幅度
    if len(results) >= 2:
        k1_train_improvement = (train_losses[0] - train_losses[1]) / train_losses[0] * 100
        k1_val_improvement = (val_losses[0] - val_losses[1]) / val_losses[0] * 100
        print(f"  K=1相对K=0训练改进: {k1_train_improvement:.2f}%")
        print(f"  K=1相对K=0验证改进: {k1_val_improvement:.2f}%")
    
    if len(results) >= 3:
        k2_train_improvement = (train_losses[1] - train_losses[2]) / train_losses[1] * 100
        k2_val_improvement = (val_losses[1] - val_losses[2]) / val_losses[1] * 100
        print(f"  K=2相对K=1训练改进: {k2_train_improvement:.2f}%")
        print(f"  K=2相对K=1验证改进: {k2_val_improvement:.2f}%")
    
    # 损失水平评估
    print(f"\n损失水平评估:")
    avg_final_loss = np.mean([r['final_val'] for r in results])
    print(f"  平均最终验证损失: {avg_final_loss:.6f}")
    
    if avg_final_loss > 3.0:
        print("  ⚠️  损失偏高，可能需要:")
        print("     - 增加训练轮数")
        print("     - 调整学习率")
        print("     - 检查数据预处理")
        print("     - 优化模型架构")
    elif avg_final_loss > 1.0:
        print("  ✅ 损失适中，继续观察收敛趋势")
    else:
        print("  🎉 损失较低，模型学习效果良好")
    
    return train_order_correct and val_order_correct

def main():
    """主函数"""
    
    print("开始K值快速测试...")
    print("测试配置: 200个样本，每个K值训练5轮")
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 测试所有K值
    k_values = [0, 1, 2]
    results = []
    
    for k in k_values:
        try:
            result = quick_test_single_k(k, num_samples=200, num_epochs=5)
            if result is not None:
                results.append(result)
        except Exception as e:
            print(f"K={k}测试失败: {e}")
            continue
    
    if not results:
        print("❌ 所有测试都失败了")
        return
    
    # 分析结果
    order_correct = analyze_results(results)
    
    print(f"\n{'='*60}")
    print("总结")
    print(f"{'='*60}")
    
    if order_correct:
        print("🎉 K值性能顺序符合预期！")
        print("   建议继续完整训练验证")
    else:
        print("⚠️  K值性能顺序不符合预期")
        print("   可能需要进一步调整:")
        print("   - 模型架构")
        print("   - 训练参数")
        print("   - 数据预处理")
    
    # 保存结果
    torch.save(results, 'quick_test_results.pth')
    print(f"\n结果已保存到 quick_test_results.pth")

if __name__ == "__main__":
    main()
