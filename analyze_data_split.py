#!/usr/bin/env python3
"""
分析数据分割问题
检查训练集和验证集的数据分布
"""

import torch
import torch.nn as nn
import numpy as np
import os
import glob

def analyze_data_distribution():
    """分析数据分布"""
    
    print("🔍 分析数据分布问题...")
    
    for k in [0]:  # 先只分析K=0
        print(f"\n分析 K={k}:")
        
        data_dir = f'filtered_dataset_k{k}'
        if not os.path.exists(data_dir):
            print(f"❌ 数据目录不存在: {data_dir}")
            continue
        
        # 加载数据
        file_paths = sorted(glob.glob(os.path.join(data_dir, 'batch_*.pth')))[:2]
        
        all_data = []
        for f_path in file_paths:
            try:
                batch_data = torch.load(f_path, map_location='cpu', weights_only=False)
                node_features = batch_data['node_features']
                expert_actions = batch_data['expert_actions']
                
                for i in range(min(100, node_features.shape[0])):
                    all_data.append(expert_actions[i])
            except Exception as e:
                print(f"加载失败: {e}")
                continue
        
        if len(all_data) < 50:
            print(f"数据不足: {len(all_data)}")
            continue
        
        print(f"总样本数: {len(all_data)}")
        
        # 计算统计信息
        all_actions = torch.stack(all_data)  # [num_samples, num_nodes, 2]
        
        # 整体统计
        overall_mean = torch.mean(all_actions)
        overall_std = torch.std(all_actions)
        overall_norm = torch.mean(torch.norm(all_actions, dim=-1))
        
        print(f"整体统计:")
        print(f"  均值: {overall_mean:.6f}")
        print(f"  标准差: {overall_std:.6f}")
        print(f"  平均范数: {overall_norm:.6f}")
        
        # 分割数据
        train_size = int(0.8 * len(all_data))
        train_actions = all_actions[:train_size]
        val_actions = all_actions[train_size:]
        
        # 训练集统计
        train_mean = torch.mean(train_actions)
        train_std = torch.std(train_actions)
        train_norm = torch.mean(torch.norm(train_actions, dim=-1))
        
        print(f"训练集统计:")
        print(f"  均值: {train_mean:.6f}")
        print(f"  标准差: {train_std:.6f}")
        print(f"  平均范数: {train_norm:.6f}")
        
        # 验证集统计
        val_mean = torch.mean(val_actions)
        val_std = torch.std(val_actions)
        val_norm = torch.mean(torch.norm(val_actions, dim=-1))
        
        print(f"验证集统计:")
        print(f"  均值: {val_mean:.6f}")
        print(f"  标准差: {val_std:.6f}")
        print(f"  平均范数: {val_norm:.6f}")
        
        # 检查分布差异
        mean_diff = abs(train_mean - val_mean)
        std_diff = abs(train_std - val_std)
        norm_diff = abs(train_norm - val_norm)
        
        print(f"分布差异:")
        print(f"  均值差异: {mean_diff:.6f}")
        print(f"  标准差差异: {std_diff:.6f}")
        print(f"  范数差异: {norm_diff:.6f}")
        
        if mean_diff > 0.1 or std_diff > 0.1 or norm_diff > 0.1:
            print("⚠️  训练集和验证集分布差异较大！")
        else:
            print("✅ 训练集和验证集分布相似")

def test_random_split_vs_sequential():
    """测试随机分割 vs 顺序分割"""
    
    print(f"\n🔍 测试不同分割方式...")
    
    # 创建简单测试数据
    torch.manual_seed(42)
    data = []
    for i in range(100):
        # 创建有趋势的数据（模拟真实情况）
        trend = i * 0.01  # 随时间增加的趋势
        sample = torch.randn(10, 2) + trend
        data.append(sample)
    
    all_data = torch.stack(data)
    
    print("测试数据统计:")
    print(f"  整体均值: {torch.mean(all_data):.6f}")
    print(f"  整体标准差: {torch.std(all_data):.6f}")
    
    # 方法1：顺序分割（前80%训练，后20%验证）
    train_size = 80
    train_sequential = all_data[:train_size]
    val_sequential = all_data[train_size:]
    
    print(f"\n顺序分割:")
    print(f"  训练集均值: {torch.mean(train_sequential):.6f}")
    print(f"  验证集均值: {torch.mean(val_sequential):.6f}")
    print(f"  均值差异: {abs(torch.mean(train_sequential) - torch.mean(val_sequential)):.6f}")
    
    # 方法2：随机分割
    indices = torch.randperm(100)
    train_indices = indices[:train_size]
    val_indices = indices[train_size:]
    
    train_random = all_data[train_indices]
    val_random = all_data[val_indices]
    
    print(f"\n随机分割:")
    print(f"  训练集均值: {torch.mean(train_random):.6f}")
    print(f"  验证集均值: {torch.mean(val_random):.6f}")
    print(f"  均值差异: {abs(torch.mean(train_random) - torch.mean(val_random)):.6f}")
    
    # 模拟训练
    print(f"\n模拟训练效果:")
    
    # 简单模型：预测均值
    def simple_loss(pred_mean, true_data):
        return torch.mean((pred_mean - true_data) ** 2)
    
    # 顺序分割的"训练"
    train_mean_seq = torch.mean(train_sequential)
    train_loss_seq = simple_loss(train_mean_seq, train_sequential)
    val_loss_seq = simple_loss(train_mean_seq, val_sequential)
    
    print(f"顺序分割:")
    print(f"  训练损失: {train_loss_seq:.6f}")
    print(f"  验证损失: {val_loss_seq:.6f}")
    print(f"  训练 >= 验证: {train_loss_seq >= val_loss_seq}")
    
    # 随机分割的"训练"
    train_mean_rand = torch.mean(train_random)
    train_loss_rand = simple_loss(train_mean_rand, train_random)
    val_loss_rand = simple_loss(train_mean_rand, val_random)
    
    print(f"随机分割:")
    print(f"  训练损失: {train_loss_rand:.6f}")
    print(f"  验证损失: {val_loss_rand:.6f}")
    print(f"  训练 >= 验证: {train_loss_rand >= val_loss_rand}")

def test_with_proper_random_split():
    """使用正确的随机分割测试"""
    
    print(f"\n🔍 使用正确随机分割测试...")
    
    # 加载真实数据
    data_dir = 'filtered_dataset_k0'
    if not os.path.exists(data_dir):
        print("❌ 数据目录不存在")
        return
    
    file_paths = sorted(glob.glob(os.path.join(data_dir, 'batch_*.pth')))[:1]
    
    all_data = []
    for f_path in file_paths:
        try:
            batch_data = torch.load(f_path, map_location='cpu', weights_only=False)
            node_features = batch_data['node_features']
            expert_actions = batch_data['expert_actions']
            
            for i in range(min(50, node_features.shape[0])):
                all_data.append((node_features[i], expert_actions[i]))
        except Exception as e:
            print(f"加载失败: {e}")
            return
    
    print(f"加载了 {len(all_data)} 个样本")
    
    # 🔧 关键：使用随机分割而不是顺序分割
    indices = torch.randperm(len(all_data))
    train_size = int(0.8 * len(all_data))
    
    train_indices = indices[:train_size]
    val_indices = indices[train_size:]
    
    train_data = [all_data[i] for i in train_indices]
    val_data = [all_data[i] for i in val_indices]
    
    print(f"随机分割: 训练{len(train_data)}, 验证{len(val_data)}")
    
    # 简单测试
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建最简单的模型
    model = nn.Sequential(
        nn.Linear(3, 32),
        nn.ReLU(),
        nn.Linear(32, 2)
    ).to(device)
    
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    print("开始简单训练...")
    
    for epoch in range(3):
        # 训练
        model.train()
        total_train_loss = 0
        
        for node_features, expert_actions in train_data:
            node_features = node_features.to(device)
            expert_actions = expert_actions.to(device)
            
            optimizer.zero_grad()
            
            # 简单：对每个节点独立预测
            pred_actions = model(node_features)
            loss = criterion(pred_actions, expert_actions)
            
            loss.backward()
            optimizer.step()
            
            total_train_loss += loss.item()
        
        avg_train_loss = total_train_loss / len(train_data)
        
        # 验证
        model.eval()
        total_val_loss = 0
        
        with torch.no_grad():
            for node_features, expert_actions in val_data:
                node_features = node_features.to(device)
                expert_actions = expert_actions.to(device)
                
                pred_actions = model(node_features)
                loss = criterion(pred_actions, expert_actions)
                
                total_val_loss += loss.item()
        
        avg_val_loss = total_val_loss / len(val_data)
        
        print(f"Epoch {epoch+1}: Train={avg_train_loss:.6f}, Val={avg_val_loss:.6f}")
        
        if avg_train_loss < avg_val_loss:
            print(f"✅ 正常：训练损失 < 验证损失")
        else:
            print(f"❌ 异常：训练损失 >= 验证损失")

def main():
    """主函数"""
    
    print("🔍 分析数据分割问题")
    
    # 1. 分析数据分布
    analyze_data_distribution()
    
    # 2. 测试分割方式
    test_random_split_vs_sequential()
    
    # 3. 使用正确分割测试
    test_with_proper_random_split()

if __name__ == "__main__":
    main()
