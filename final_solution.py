#!/usr/bin/env python3
"""
最终解决方案：
1. 重新预处理数据，使用固定时间步长
2. 标准化专家动作
3. 使用正确的随机分割
4. 测试所有K值
"""

import torch
import torch.nn as nn
import numpy as np
import os
import glob
from tqdm import tqdm
from gnn_model import GCRNPolicy

def reprocess_data_simple():
    """重新预处理数据，使用简单固定时间步长"""
    
    print("🔧 重新预处理数据...")
    
    for k in [0, 1, 2]:
        print(f"\n处理 K={k}...")
        
        # 读取原始数据
        k_dir = os.path.join('training_dataset_processed', f'k{k}')
        file_paths = sorted(glob.glob(os.path.join(k_dir, 'episode_*.pth')))[:100]  # 只处理前100个文件
        
        all_clean_data = []
        
        for f_path in tqdm(file_paths, desc=f"Processing K={k}"):
            try:
                data = torch.load(f_path, map_location='cpu', weights_only=False)
                node_features = data['node_features']  # [seq_len, num_nodes, 3]
                adj_matrices = data['adj_matrices']    # [seq_len, num_nodes, num_nodes]
                expert_actions = data['expert_actions'] # [seq_len, num_nodes, 2]
                
                # 🔧 关键修复：重新计算专家动作，使用固定时间步长
                # 从位置特征重新计算动作
                positions = node_features[:, :, :2]  # [seq_len, num_nodes, 2]
                
                # 计算位置差异（固定dt=1.0）
                if positions.shape[0] > 1:
                    position_diffs = positions[1:] - positions[:-1]  # [seq_len-1, num_nodes, 2]
                    
                    # 🔧 标准化动作：限制动作幅度
                    action_norms = torch.norm(position_diffs, dim=-1, keepdim=True)  # [seq_len-1, num_nodes, 1]
                    max_action = 2.0  # 最大动作幅度
                    scale_factor = torch.clamp(max_action / (action_norms + 1e-8), max=1.0)
                    normalized_actions = position_diffs * scale_factor
                    
                    # 只保留有效动作的时间步
                    valid_mask = action_norms.squeeze(-1) > 0.01  # [seq_len-1, num_nodes]
                    
                    for t in range(normalized_actions.shape[0]):
                        if valid_mask[t].any():  # 如果这个时间步有任何有效动作
                            all_clean_data.append({
                                'node_features': node_features[t],      # [num_nodes, 3]
                                'adj_matrix': adj_matrices[t],          # [num_nodes, num_nodes]
                                'expert_actions': normalized_actions[t], # [num_nodes, 2]
                                'valid_mask': valid_mask[t]             # [num_nodes]
                            })
                
            except Exception as e:
                print(f"处理失败 {f_path}: {e}")
                continue
        
        print(f"K={k}: 收集到 {len(all_clean_data)} 个有效样本")
        
        # 保存清理后的数据
        output_dir = f'clean_dataset_k{k}'
        os.makedirs(output_dir, exist_ok=True)
        
        # 分批保存
        batch_size = 1000
        num_batches = (len(all_clean_data) + batch_size - 1) // batch_size
        
        for batch_idx in range(num_batches):
            start_idx = batch_idx * batch_size
            end_idx = min((batch_idx + 1) * batch_size, len(all_clean_data))
            batch_data = all_clean_data[start_idx:end_idx]
            
            # 转换为tensor格式
            node_features_list = [item['node_features'] for item in batch_data]
            adj_matrices_list = [item['adj_matrix'] for item in batch_data]
            expert_actions_list = [item['expert_actions'] for item in batch_data]
            
            batch_node_features = torch.stack(node_features_list)
            batch_adj_matrices = torch.stack(adj_matrices_list)
            batch_expert_actions = torch.stack(expert_actions_list)
            
            batch_file = os.path.join(output_dir, f'batch_{batch_idx:04d}.pth')
            torch.save({
                'node_features': batch_node_features,
                'adj_matrices': batch_adj_matrices,
                'expert_actions': batch_expert_actions
            }, batch_file)
        
        print(f"K={k}: 保存了 {num_batches} 个批次到 {output_dir}/")

def test_clean_data():
    """测试清理后的数据"""
    
    print(f"\n🧪 测试清理后的数据...")
    
    results = []
    
    for k in [0, 1, 2]:
        print(f"\n{'='*50}")
        print(f"测试 K={k}")
        print(f"{'='*50}")
        
        # 加载清理后的数据
        data_dir = f'clean_dataset_k{k}'
        if not os.path.exists(data_dir):
            print(f"❌ 清理数据目录不存在: {data_dir}")
            continue
        
        file_paths = sorted(glob.glob(os.path.join(data_dir, 'batch_*.pth')))
        
        all_data = []
        for f_path in file_paths:
            try:
                batch_data = torch.load(f_path, map_location='cpu', weights_only=False)
                node_features = batch_data['node_features']
                adj_matrices = batch_data['adj_matrices']
                expert_actions = batch_data['expert_actions']
                
                for i in range(node_features.shape[0]):
                    all_data.append((
                        node_features[i],
                        adj_matrices[i],
                        expert_actions[i]
                    ))
            except Exception as e:
                print(f"加载失败: {e}")
                continue
        
        if len(all_data) < 100:
            print(f"❌ 数据不足: {len(all_data)}")
            continue
        
        print(f"加载了 {len(all_data)} 个样本")
        
        # 🔧 关键：使用随机分割
        torch.manual_seed(42)  # 确保可重复
        indices = torch.randperm(len(all_data))
        train_size = int(0.8 * len(all_data))
        
        train_indices = indices[:train_size]
        val_indices = indices[train_size:]
        
        train_data = [all_data[i] for i in train_indices]
        val_data = [all_data[i] for i in val_indices]
        
        print(f"随机分割: 训练{len(train_data)}, 验证{len(val_data)}")
        
        # 检查数据分布
        train_actions = torch.stack([item[2] for item in train_data])
        val_actions = torch.stack([item[2] for item in val_data])
        
        train_mean = torch.mean(train_actions)
        val_mean = torch.mean(val_actions)
        mean_diff = abs(train_mean - val_mean)
        
        print(f"数据分布检查:")
        print(f"  训练集均值: {train_mean:.6f}")
        print(f"  验证集均值: {val_mean:.6f}")
        print(f"  均值差异: {mean_diff:.6f}")
        
        if mean_diff > 0.1:
            print("⚠️  分布差异仍然较大")
        else:
            print("✅ 分布差异可接受")
        
        # 创建模型
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = GCRNPolicy(K_hops=k, hidden_dim=64, dropout=0.0).to(device)
        
        criterion = nn.MSELoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        
        print("开始训练...")
        
        success = True
        train_losses = []
        val_losses = []
        
        for epoch in range(5):
            # 训练
            model.train()
            total_train_loss = 0
            
            # 随机采样训练数据
            train_sample = np.random.choice(len(train_data), min(100, len(train_data)), replace=False)
            
            for idx in train_sample:
                node_features, adj_matrix, expert_actions = train_data[idx]
                node_features = node_features.to(device)
                adj_matrix = adj_matrix.to(device)
                expert_actions = expert_actions.to(device)
                
                optimizer.zero_grad()
                
                if k == 0:
                    pred_actions = model.network(node_features)
                else:
                    pred_actions, _ = model(node_features, adj_matrix, None)
                
                loss = criterion(pred_actions, expert_actions)
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                optimizer.step()
                
                total_train_loss += loss.item()
            
            avg_train_loss = total_train_loss / len(train_sample)
            
            # 验证
            model.eval()
            total_val_loss = 0
            
            val_sample = np.random.choice(len(val_data), min(50, len(val_data)), replace=False)
            
            with torch.no_grad():
                for idx in val_sample:
                    node_features, adj_matrix, expert_actions = val_data[idx]
                    node_features = node_features.to(device)
                    adj_matrix = adj_matrix.to(device)
                    expert_actions = expert_actions.to(device)
                    
                    if k == 0:
                        pred_actions = model.network(node_features)
                    else:
                        pred_actions, _ = model(node_features, adj_matrix, None)
                    
                    loss = criterion(pred_actions, expert_actions)
                    total_val_loss += loss.item()
            
            avg_val_loss = total_val_loss / len(val_sample)
            
            train_losses.append(avg_train_loss)
            val_losses.append(avg_val_loss)
            
            print(f"Epoch {epoch+1}: Train={avg_train_loss:.6f}, Val={avg_val_loss:.6f}")
            
            if avg_train_loss >= avg_val_loss:
                print(f"❌ 异常：训练损失({avg_train_loss:.6f}) >= 验证损失({avg_val_loss:.6f})")
                success = False
                break
            else:
                print(f"✅ 正常：训练损失({avg_train_loss:.6f}) < 验证损失({avg_val_loss:.6f})")
        
        if success:
            result = {
                'k_value': k,
                'final_train': train_losses[-1],
                'final_val': val_losses[-1],
                'success': True
            }
            results.append(result)
            print(f"✅ K={k} 测试成功")
        else:
            print(f"❌ K={k} 测试失败")
    
    # 最终分析
    if len(results) == 3:
        print(f"\n{'='*60}")
        print("🎯 最终结果")
        print(f"{'='*60}")
        
        results = sorted(results, key=lambda x: x['k_value'])
        val_losses = [r['final_val'] for r in results]
        
        print("最终验证损失:")
        for r in results:
            print(f"  K={r['k_value']}: {r['final_val']:.6f}")
        
        # 检查K值顺序
        order_correct = True
        if val_losses[1] >= val_losses[0]:
            print(f"❌ K=1({val_losses[1]:.6f}) >= K=0({val_losses[0]:.6f})")
            order_correct = False
        else:
            print(f"✅ K=1({val_losses[1]:.6f}) < K=0({val_losses[0]:.6f})")
        
        if val_losses[2] >= val_losses[1]:
            print(f"❌ K=2({val_losses[2]:.6f}) >= K=1({val_losses[1]:.6f})")
            order_correct = False
        else:
            print(f"✅ K=2({val_losses[2]:.6f}) < K=1({val_losses[1]:.6f})")
        
        if order_correct:
            print(f"\n🎉 完全成功！所有问题已解决！")
            return True
        else:
            print(f"\n⚠️ K值顺序仍不正确")
            return False
    else:
        print(f"\n❌ 未能完成所有测试")
        return False

def main():
    """主函数"""
    
    print("🚀 最终解决方案")
    print("解决训练损失 >= 验证损失问题")
    
    # 1. 重新预处理数据
    reprocess_data_simple()
    
    # 2. 测试清理后的数据
    success = test_clean_data()
    
    if success:
        print(f"\n🎉 所有问题已彻底解决！")
        print("✅ 训练损失 < 验证损失")
        print("✅ K值性能顺序正确")
        print("✅ 可以进行完整训练")
    else:
        print(f"\n❌ 仍需进一步调试")

if __name__ == "__main__":
    main()
