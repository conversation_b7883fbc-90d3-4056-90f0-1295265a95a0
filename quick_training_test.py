#!/usr/bin/env python3
"""
快速训练测试，验证K=0问题是否解决
"""

import torch
import torch.nn as nn
import numpy as np
from gnn_model import GCRNPolicy
from training import Trainer, CoverageSequenceDataset
import os

def create_synthetic_data(num_episodes=50, sequence_length=20, num_robots=5):
    """创建合成训练数据"""
    
    print("创建合成训练数据...")
    
    all_data = []
    
    for episode in range(num_episodes):
        # 随机生成节点特征序列
        node_features = torch.randn(sequence_length, num_robots, 3)
        
        # 生成不同K值的邻接矩阵
        adj_matrices = []
        for t in range(sequence_length):
            # 随机邻接矩阵（对称）
            adj = torch.rand(num_robots, num_robots)
            adj = (adj + adj.T) / 2
            adj = adj + torch.eye(num_robots)  # 添加自环
            adj_matrices.append(adj)
        adj_matrices = torch.stack(adj_matrices)
        
        # 生成目标动作（基于特征的简单函数）
        expert_actions = torch.randn(sequence_length, num_robots, 2)
        
        all_data.append((node_features, adj_matrices, expert_actions))
    
    return CoverageSequenceDataset(all_data)

def quick_training_comparison():
    """快速训练比较"""
    
    print("=" * 60)
    print("快速训练比较测试")
    print("=" * 60)
    
    # 创建合成数据
    dataset = create_synthetic_data(num_episodes=100, sequence_length=15, num_robots=5)
    
    # 分割数据
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    def seq_collate_fn(batch):
        return batch[0]
    
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=1, shuffle=True, collate_fn=seq_collate_fn
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=1, shuffle=False, collate_fn=seq_collate_fn
    )
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 测试不同K值
    k_values = [0, 1, 2]
    results = {}
    
    for k in k_values:
        print(f"\n训练 K={k} 模型...")
        
        # 创建模型
        model = GCRNPolicy(K_hops=k, hidden_dim=64, in_features=3).to(device)
        
        # 创建训练器
        trainer = Trainer(
            model, 
            learning_rate=0.001, 
            weight_decay=1e-4, 
            device=device, 
            direct_prediction=True
        )
        
        # 训练几个epoch
        epochs = 10
        train_losses = []
        val_losses = []
        
        for epoch in range(epochs):
            # 训练
            model.train()
            total_train_loss = 0
            for sequence_data in train_loader:
                loss = trainer._process_sequence(sequence_data, is_training=True)
                total_train_loss += loss
            avg_train_loss = total_train_loss / len(train_loader)
            
            # 验证
            avg_val_loss = trainer._run_eval_loop(val_loader)
            
            train_losses.append(avg_train_loss)
            val_losses.append(avg_val_loss)
            
            if (epoch + 1) % 5 == 0:
                print(f"  Epoch {epoch+1}: Train={avg_train_loss:.6f}, Val={avg_val_loss:.6f}")
        
        results[k] = {
            'train_losses': train_losses,
            'val_losses': val_losses,
            'final_train': train_losses[-1],
            'final_val': val_losses[-1],
            'params': sum(p.numel() for p in model.parameters())
        }
    
    # 分析结果
    print(f"\n" + "=" * 60)
    print("训练结果分析")
    print("=" * 60)
    
    print("参数数量:")
    for k in k_values:
        print(f"  K={k}: {results[k]['params']:,}")
    
    print(f"\n最终训练损失:")
    for k in k_values:
        print(f"  K={k}: {results[k]['final_train']:.6f}")
    
    print(f"\n最终验证损失:")
    for k in k_values:
        print(f"  K={k}: {results[k]['final_val']:.6f}")
    
    # 检查是否解决了K=0问题
    train_losses = [results[k]['final_train'] for k in k_values]
    val_losses = [results[k]['final_val'] for k in k_values]
    
    train_std = np.std(train_losses)
    val_std = np.std(val_losses)
    
    print(f"\n损失标准差:")
    print(f"  训练损失标准差: {train_std:.6f}")
    print(f"  验证损失标准差: {val_std:.6f}")
    
    # 检查K=0是否仍然异常优秀
    k0_train = results[0]['final_train']
    k0_val = results[0]['final_val']
    
    other_train_avg = np.mean([results[k]['final_train'] for k in [1, 2]])
    other_val_avg = np.mean([results[k]['final_val'] for k in [1, 2]])
    
    train_advantage = (other_train_avg - k0_train) / other_train_avg * 100
    val_advantage = (other_val_avg - k0_val) / other_val_avg * 100
    
    print(f"\nK=0相对优势:")
    print(f"  训练损失优势: {train_advantage:.2f}%")
    print(f"  验证损失优势: {val_advantage:.2f}%")
    
    # 判断是否解决问题
    if abs(train_advantage) < 5 and abs(val_advantage) < 5:
        print(f"\n🎉 K=0问题已解决！")
        print(f"   K=0不再有显著优势，可以进行公平比较")
        return True
    else:
        print(f"\n⚠️  K=0仍有优势，需要进一步调整")
        return False

def main():
    """主函数"""
    
    print("开始快速训练测试...")
    
    # 设置随机种子确保可重现性
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 运行测试
    success = quick_training_comparison()
    
    if success:
        print(f"\n✅ 统一架构解决方案成功！")
        print(f"建议：")
        print(f"1. 删除旧的训练模型")
        print(f"2. 使用新架构重新训练完整数据集")
        print(f"3. 比较新的训练结果")
    else:
        print(f"\n❌ 需要进一步优化架构或训练策略")

if __name__ == "__main__":
    main()
