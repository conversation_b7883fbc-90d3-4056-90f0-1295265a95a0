#!/usr/bin/env python3
"""
最终修复测试：使用完全确定性的设置
"""

import torch
import torch.nn as nn
import numpy as np
import os
import glob
from gnn_model import GCRNPolicy

def set_deterministic():
    """设置完全确定性"""
    torch.manual_seed(42)
    np.random.seed(42)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def test_final_k_values():
    """最终K值测试，使用修复后的模型"""
    
    print("🚀 最终修复测试")
    print("使用完全无Dropout的模型")
    
    set_deterministic()
    
    # 加载真实数据进行测试
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    results = []
    
    for k in [0, 1, 2]:
        print(f"\n{'='*50}")
        print(f"🧪 测试 K={k}")
        print(f"{'='*50}")
        
        # 加载过滤数据
        data_dir = f'filtered_dataset_k{k}'
        if not os.path.exists(data_dir):
            print(f"❌ 数据目录不存在: {data_dir}")
            continue
        
        # 加载少量数据进行快速测试
        file_paths = sorted(glob.glob(os.path.join(data_dir, 'batch_*.pth')))[:2]  # 只用前2个batch
        
        all_data = []
        for f_path in file_paths:
            try:
                batch_data = torch.load(f_path, map_location='cpu', weights_only=False)
                node_features = batch_data['node_features']  # [batch_size, num_nodes, 3]
                adj_matrices = batch_data['adj_matrices']    # [batch_size, num_nodes, num_nodes]
                expert_actions = batch_data['expert_actions'] # [batch_size, num_nodes, 2]
                
                # 只取前100个样本
                for i in range(min(100, node_features.shape[0])):
                    all_data.append((
                        node_features[i].to(device),
                        adj_matrices[i].to(device),
                        expert_actions[i].to(device)
                    ))
            except Exception as e:
                print(f"加载失败 {f_path}: {e}")
                continue
        
        if len(all_data) < 50:
            print(f"❌ K={k}数据不足: {len(all_data)}")
            continue
        
        print(f"加载了 {len(all_data)} 个样本")
        
        # 分割数据
        train_data = all_data[:int(0.8 * len(all_data))]
        val_data = all_data[int(0.8 * len(all_data)):]
        
        print(f"训练样本: {len(train_data)}, 验证样本: {len(val_data)}")
        
        # 创建模型（完全无dropout）
        model = GCRNPolicy(K_hops=k, hidden_dim=64, dropout=0.0).to(device)
        
        # 固定模型权重以确保确定性
        for param in model.parameters():
            param.data.fill_(0.1)  # 固定权重
        
        criterion = nn.MSELoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        
        print("开始训练...")
        
        success = True
        train_losses = []
        val_losses = []
        
        for epoch in range(3):  # 只训练3轮
            # 训练
            model.train()
            total_train_loss = 0
            
            for node_features, adj_matrix, expert_actions in train_data:
                optimizer.zero_grad()
                
                # 前向传播
                if k == 0:
                    pred_actions = model.network(node_features)
                else:
                    pred_actions, _ = model(node_features, adj_matrix, None)
                
                loss = criterion(pred_actions, expert_actions)
                loss.backward()
                optimizer.step()
                
                total_train_loss += loss.item()
            
            avg_train_loss = total_train_loss / len(train_data)
            
            # 验证
            model.eval()
            total_val_loss = 0
            
            with torch.no_grad():
                for node_features, adj_matrix, expert_actions in val_data:
                    if k == 0:
                        pred_actions = model.network(node_features)
                    else:
                        pred_actions, _ = model(node_features, adj_matrix, None)
                    
                    loss = criterion(pred_actions, expert_actions)
                    total_val_loss += loss.item()
            
            avg_val_loss = total_val_loss / len(val_data)
            
            train_losses.append(avg_train_loss)
            val_losses.append(avg_val_loss)
            
            print(f"Epoch {epoch+1}: Train={avg_train_loss:.6f}, Val={avg_val_loss:.6f}")
            
            # 🔍 严格检查
            if avg_train_loss >= avg_val_loss:
                print(f"❌ 异常：训练损失({avg_train_loss:.6f}) >= 验证损失({avg_val_loss:.6f})")
                success = False
                break
            else:
                print(f"✅ 正常：训练损失({avg_train_loss:.6f}) < 验证损失({avg_val_loss:.6f})")
        
        if success:
            result = {
                'k_value': k,
                'final_train': train_losses[-1],
                'final_val': val_losses[-1],
                'success': True
            }
            results.append(result)
            print(f"✅ K={k} 测试成功")
        else:
            print(f"❌ K={k} 测试失败")
    
    # 分析结果
    if len(results) == 3:
        print(f"\n{'='*60}")
        print("🎯 最终结果分析")
        print(f"{'='*60}")
        
        results = sorted(results, key=lambda x: x['k_value'])
        
        print("最终损失:")
        for r in results:
            print(f"  K={r['k_value']}: Train={r['final_train']:.6f}, Val={r['final_val']:.6f}")
        
        # 检查K值性能顺序
        val_losses = [r['final_val'] for r in results]
        
        order_correct = True
        if val_losses[1] >= val_losses[0]:  # K=1应该比K=0好
            print(f"❌ K=1({val_losses[1]:.6f}) >= K=0({val_losses[0]:.6f})")
            order_correct = False
        else:
            print(f"✅ K=1({val_losses[1]:.6f}) < K=0({val_losses[0]:.6f})")
        
        if val_losses[2] >= val_losses[1]:  # K=2应该比K=1好
            print(f"❌ K=2({val_losses[2]:.6f}) >= K=1({val_losses[1]:.6f})")
            order_correct = False
        else:
            print(f"✅ K=2({val_losses[2]:.6f}) < K=1({val_losses[1]:.6f})")
        
        if order_correct:
            print(f"\n🎉 完全成功！")
            print("✅ 训练损失 < 验证损失")
            print("✅ K值性能顺序正确")
            return True
        else:
            print(f"\n⚠️ K值性能顺序不正确")
            return False
    else:
        print(f"\n❌ 未能完成所有K值测试")
        return False

def main():
    """主函数"""
    
    print("🔧 最终修复测试")
    print("已移除所有Dropout和LayerNorm")
    
    success = test_final_k_values()
    
    if success:
        print(f"\n🎉 所有问题已解决！")
        print("可以进行完整训练了")
    else:
        print(f"\n❌ 仍有问题需要解决")
        print("建议检查:")
        print("1. 模型架构中是否还有其他状态层")
        print("2. 数据预处理逻辑")
        print("3. 损失函数实现")

if __name__ == "__main__":
    main()
