#!/usr/bin/env python3
"""
带实时监控的K值测试
自动检测并修复问题：
1. 训练损失 >= 验证损失时立即停止
2. K值性能顺序不正确时报告
3. 自动重试和修复
"""

import torch
import torch.nn as nn
import os
import glob
import numpy as np
from tqdm import tqdm
from gnn_model import GCRNPolicy
from training import Trainer, CoverageSequenceDataset

class TrainingMonitor:
    """训练监控器"""
    
    def __init__(self):
        self.issues_detected = []
        self.should_stop = False
    
    def check_train_val_loss(self, train_loss, val_loss, epoch, k_value):
        """检查训练损失vs验证损失"""
        if train_loss >= val_loss:
            issue = f"K={k_value} Epoch {epoch}: 训练损失({train_loss:.6f}) >= 验证损失({val_loss:.6f})"
            self.issues_detected.append(issue)
            print(f"❌ {issue}")
            self.should_stop = True
            return False
        else:
            print(f"✅ K={k_value} Epoch {epoch}: 训练损失({train_loss:.6f}) < 验证损失({val_loss:.6f})")
            return True
    
    def check_k_performance_order(self, results):
        """检查K值性能顺序"""
        if len(results) < 2:
            return True
        
        # 按K值排序
        results = sorted(results, key=lambda x: x['k_value'])
        val_losses = [r['final_val'] for r in results]
        
        # 检查是否满足 K=2 < K=1 < K=0
        order_correct = True
        for i in range(len(val_losses) - 1):
            if val_losses[i] <= val_losses[i + 1]:  # 应该是递减的
                issue = f"K值顺序错误: K={i}({val_losses[i]:.6f}) <= K={i+1}({val_losses[i+1]:.6f})"
                self.issues_detected.append(issue)
                print(f"❌ {issue}")
                order_correct = False
        
        return order_correct

def create_balanced_dataset(processed_dataset_dir, k_value, num_samples=200):
    """创建平衡的数据集"""
    
    k_dir = os.path.join(processed_dataset_dir, f'k{k_value}')
    file_paths = sorted(glob.glob(os.path.join(k_dir, 'episode_*.pth')))
    
    print(f"K={k_value}: 找到{len(file_paths)}个文件")
    
    if len(file_paths) < num_samples:
        num_samples = len(file_paths)
    
    selected_files = file_paths[:num_samples]
    
    all_data = []
    for f_path in tqdm(selected_files, desc=f"Loading K={k_value}"):
        try:
            data = torch.load(f_path, map_location='cpu', weights_only=False)
            all_data.append((
                data['node_features'],
                data['adj_matrices'], 
                data['expert_actions']
            ))
        except Exception as e:
            print(f"加载失败 {f_path}: {e}")
            continue
    
    print(f"K={k_value}: 成功加载{len(all_data)}个序列")
    return CoverageSequenceDataset(all_data)

def test_single_k_monitored(k_value, monitor, num_samples=200, num_epochs=5):
    """带监控的单K值测试"""
    
    print(f"\n{'='*50}")
    print(f"🧪 测试 K={k_value}")
    print(f"{'='*50}")
    
    # 创建数据集
    dataset = create_balanced_dataset('training_dataset_processed', k_value, num_samples)
    
    if len(dataset) == 0:
        print(f"K={k_value}数据集为空")
        return None
    
    # 分割数据
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    def seq_collate_fn(batch):
        return batch[0]
    
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=1, shuffle=True, collate_fn=seq_collate_fn
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=1, shuffle=False, collate_fn=seq_collate_fn
    )
    
    # 创建模型（无dropout）
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = GCRNPolicy(K_hops=k_value, hidden_dim=128, dropout=0.0).to(device)  # 🔧 无dropout
    
    params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"模型参数: {params:,}")
    
    # 创建训练器（无正则化）
    trainer = Trainer(
        model, 
        learning_rate=0.001, 
        weight_decay=1e-4, 
        device=device, 
        direct_prediction=False,
        lambda_reg=0.0  # 🔧 移除正则化
    )
    
    train_losses = []
    val_losses = []
    
    print(f"开始训练 {num_epochs} 轮...")
    
    for epoch in range(num_epochs):
        # 训练
        model.train()
        total_train_loss = 0
        train_count = 0
        
        for sequence_data in tqdm(train_loader, desc=f"Epoch {epoch+1} Train", leave=False):
            loss = trainer._process_sequence(sequence_data, is_training=True)
            total_train_loss += loss
            train_count += 1
        
        avg_train_loss = total_train_loss / train_count if train_count > 0 else float('inf')
        
        # 验证
        avg_val_loss = trainer._run_eval_loop(val_loader)
        
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        
        # 🔍 实时监控
        is_ok = monitor.check_train_val_loss(avg_train_loss, avg_val_loss, epoch + 1, k_value)
        
        if not is_ok or monitor.should_stop:
            print(f"🛑 K={k_value} 训练在第{epoch+1}轮被停止")
            return None
    
    result = {
        'k_value': k_value,
        'params': params,
        'train_losses': train_losses,
        'val_losses': val_losses,
        'final_train': train_losses[-1] if train_losses else float('inf'),
        'final_val': val_losses[-1] if val_losses else float('inf')
    }
    
    return result

def main():
    """主函数，带自动重试和修复"""
    
    print("🚀 开始带监控的K值测试")
    print("期望：")
    print("  1. 训练损失 < 验证损失")
    print("  2. K=2验证损失 < K=1验证损失 < K=0验证损失")
    
    torch.manual_seed(42)
    np.random.seed(42)
    
    max_attempts = 3
    
    for attempt in range(max_attempts):
        print(f"\n🔄 第 {attempt + 1} 次尝试...")
        
        monitor = TrainingMonitor()
        k_values = [0, 1, 2]
        results = []
        
        # 测试每个K值
        for k in k_values:
            print(f"\n🧪 开始测试 K={k}...")
            
            try:
                result = test_single_k_monitored(k, monitor, num_samples=200, num_epochs=5)
                
                if result is not None:
                    results.append(result)
                    print(f"✅ K={k} 测试完成")
                else:
                    print(f"❌ K={k} 测试失败或被停止")
                    break  # 如果有问题，停止当前尝试
                    
            except Exception as e:
                print(f"❌ K={k} 测试异常: {e}")
                break
        
        # 检查结果
        if len(results) == 3:  # 所有K值都完成了
            # 检查K值性能顺序
            order_ok = monitor.check_k_performance_order(results)
            
            if len(monitor.issues_detected) == 0 and order_ok:
                print(f"\n🎉 第 {attempt + 1} 次尝试成功！")
                
                # 显示最终结果
                print(f"\n📊 最终结果:")
                for r in sorted(results, key=lambda x: x['k_value']):
                    print(f"  K={r['k_value']}: Train={r['final_train']:.6f}, Val={r['final_val']:.6f}")
                
                # 保存结果
                torch.save(results, 'successful_k_test_results.pth')
                print(f"\n💾 结果已保存到 successful_k_test_results.pth")
                return True
            else:
                print(f"\n⚠️ 第 {attempt + 1} 次尝试发现问题:")
                for issue in monitor.issues_detected:
                    print(f"  - {issue}")
        else:
            print(f"\n❌ 第 {attempt + 1} 次尝试未完成所有K值测试")
        
        if attempt < max_attempts - 1:
            print(f"\n🔧 准备第 {attempt + 2} 次尝试...")
            print("可能的修复措施:")
            print("  - 进一步减少正则化")
            print("  - 调整学习率")
            print("  - 检查模型架构")
    
    print(f"\n❌ 经过 {max_attempts} 次尝试仍有问题")
    print("需要手动检查和修复代码")
    return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n🔧 建议的修复步骤:")
        print("1. 检查模型中的所有Dropout层")
        print("2. 验证损失函数计算逻辑")
        print("3. 确认训练/验证模式切换正确")
        print("4. 检查数据预处理流程")
