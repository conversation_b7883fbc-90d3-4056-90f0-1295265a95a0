#!/usr/bin/env python3
"""
最终K值测试，使用过滤后的数据
严格监控：训练损失 < 验证损失
"""

import torch
import torch.nn as nn
import os
import glob
import numpy as np
from tqdm import tqdm
from gnn_model import GCRNPolicy
from training import Trainer

class FilteredDataset(torch.utils.data.Dataset):
    """过滤后的数据集"""
    
    def __init__(self, data_dir, max_samples=None):
        self.data_files = sorted(glob.glob(os.path.join(data_dir, 'batch_*.pth')))
        self.data = []
        
        print(f"加载数据从 {data_dir}...")
        
        total_loaded = 0
        for f_path in tqdm(self.data_files, desc="Loading batches"):
            try:
                batch_data = torch.load(f_path, map_location='cpu', weights_only=False)
                
                node_features = batch_data['node_features']  # [batch_size, num_nodes, 3]
                adj_matrices = batch_data['adj_matrices']    # [batch_size, num_nodes, num_nodes]
                expert_actions = batch_data['expert_actions'] # [batch_size, num_nodes, 2]
                
                # 将每个样本作为独立项
                for i in range(node_features.shape[0]):
                    self.data.append((
                        node_features[i],   # [num_nodes, 3]
                        adj_matrices[i],    # [num_nodes, num_nodes]
                        expert_actions[i]   # [num_nodes, 2]
                    ))
                    total_loaded += 1
                    
                    if max_samples and total_loaded >= max_samples:
                        break
                        
                if max_samples and total_loaded >= max_samples:
                    break
                    
            except Exception as e:
                print(f"加载失败 {f_path}: {e}")
                continue
        
        print(f"成功加载 {len(self.data)} 个样本")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        return self.data[idx]

def test_single_k_final(k_value, max_samples=5000, num_epochs=5):
    """最终K值测试"""
    
    print(f"\n{'='*50}")
    print(f"🧪 最终测试 K={k_value}")
    print(f"{'='*50}")
    
    # 加载过滤后的数据
    data_dir = f'filtered_dataset_k{k_value}'
    if not os.path.exists(data_dir):
        print(f"❌ 数据目录不存在: {data_dir}")
        return None
    
    dataset = FilteredDataset(data_dir, max_samples)
    
    if len(dataset) == 0:
        print(f"❌ K={k_value}数据集为空")
        return None
    
    # 分割数据
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=32, shuffle=True
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=32, shuffle=False
    )
    
    print(f"训练样本: {len(train_dataset)}, 验证样本: {len(val_dataset)}")
    
    # 创建模型（无dropout）
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = GCRNPolicy(K_hops=k_value, hidden_dim=128, dropout=0.0).to(device)
    
    params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"模型参数: {params:,}")
    
    # 创建训练器（无正则化）
    trainer = Trainer(
        model, 
        learning_rate=0.001, 
        weight_decay=1e-4, 
        device=device, 
        direct_prediction=False,
        lambda_reg=0.0  # 🔧 无正则化
    )
    
    train_losses = []
    val_losses = []
    
    print(f"开始训练 {num_epochs} 轮...")
    
    for epoch in range(num_epochs):
        # 训练
        model.train()
        total_train_loss = 0
        train_count = 0
        
        train_pbar = tqdm(train_loader, desc=f"Epoch {epoch+1} Train", leave=False)
        for batch in train_pbar:
            node_features, adj_matrix, expert_actions = [x.to(device) for x in batch]
            
            trainer.optimizer.zero_grad()
            
            # 前向传播
            if trainer.direct_prediction:
                pred_actions, _ = model(node_features, adj_matrix, None)
                loss = trainer.criterion(pred_actions, expert_actions)
            else:
                # 修正量学习
                local_pos_diff = node_features[:, :, :2]  # [batch, nodes, 2]
                local_mass = node_features[:, :, 2:3]     # [batch, nodes, 1]
                u_local = 2 * local_mass * local_pos_diff # [batch, nodes, 2]
                
                delta_u, _ = model(node_features, adj_matrix, None)
                u_pred = u_local + delta_u
                
                loss = trainer.criterion(u_pred, expert_actions)
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            trainer.optimizer.step()
            
            total_train_loss += loss.item()
            train_count += 1
            train_pbar.set_postfix({'loss': f'{loss.item():.4f}'})
        
        avg_train_loss = total_train_loss / train_count if train_count > 0 else float('inf')
        
        # 验证
        model.eval()
        total_val_loss = 0
        val_count = 0
        
        with torch.no_grad():
            for batch in tqdm(val_loader, desc=f"Epoch {epoch+1} Val", leave=False):
                node_features, adj_matrix, expert_actions = [x.to(device) for x in batch]
                
                if trainer.direct_prediction:
                    pred_actions, _ = model(node_features, adj_matrix, None)
                    loss = trainer.criterion(pred_actions, expert_actions)
                else:
                    local_pos_diff = node_features[:, :, :2]
                    local_mass = node_features[:, :, 2:3]
                    u_local = 2 * local_mass * local_pos_diff
                    
                    delta_u, _ = model(node_features, adj_matrix, None)
                    u_pred = u_local + delta_u
                    
                    loss = trainer.criterion(u_pred, expert_actions)
                
                total_val_loss += loss.item()
                val_count += 1
        
        avg_val_loss = total_val_loss / val_count if val_count > 0 else float('inf')
        
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        
        print(f"Epoch {epoch+1}: Train={avg_train_loss:.6f}, Val={avg_val_loss:.6f}")
        
        # 🔍 严格检查
        if avg_train_loss >= avg_val_loss:
            print(f"❌ 异常检测：训练损失({avg_train_loss:.6f}) >= 验证损失({avg_val_loss:.6f})")
            print(f"🛑 停止训练 K={k_value}")
            return None
        else:
            print(f"✅ 正常：训练损失({avg_train_loss:.6f}) < 验证损失({avg_val_loss:.6f})")
    
    result = {
        'k_value': k_value,
        'params': params,
        'train_losses': train_losses,
        'val_losses': val_losses,
        'final_train': train_losses[-1] if train_losses else float('inf'),
        'final_val': val_losses[-1] if val_losses else float('inf'),
        'samples': len(dataset)
    }
    
    return result

def main():
    """主函数"""
    
    print("🚀 开始最终K值测试")
    print("使用过滤后的数据，严格监控训练过程")
    
    torch.manual_seed(42)
    np.random.seed(42)
    
    max_attempts = 3
    
    for attempt in range(max_attempts):
        print(f"\n🔄 第 {attempt + 1} 次尝试...")
        
        results = []
        success = True
        
        # 测试所有K值
        for k in [0, 1, 2]:
            print(f"\n🧪 测试 K={k}...")
            
            try:
                result = test_single_k_final(k, max_samples=5000, num_epochs=5)
                
                if result is not None:
                    results.append(result)
                    print(f"✅ K={k} 测试成功")
                else:
                    print(f"❌ K={k} 测试失败")
                    success = False
                    break
                    
            except Exception as e:
                print(f"❌ K={k} 测试异常: {e}")
                success = False
                break
        
        if success and len(results) == 3:
            # 检查K值性能顺序
            results = sorted(results, key=lambda x: x['k_value'])
            val_losses = [r['final_val'] for r in results]
            
            print(f"\n📊 最终结果:")
            for r in results:
                print(f"  K={r['k_value']}: 样本={r['samples']:,}, Train={r['final_train']:.6f}, Val={r['final_val']:.6f}")
            
            # 检查性能顺序
            order_correct = True
            if val_losses[1] >= val_losses[0]:  # K=1应该比K=0好
                print(f"❌ K=1({val_losses[1]:.6f}) >= K=0({val_losses[0]:.6f})")
                order_correct = False
            
            if val_losses[2] >= val_losses[1]:  # K=2应该比K=1好
                print(f"❌ K=2({val_losses[2]:.6f}) >= K=1({val_losses[1]:.6f})")
                order_correct = False
            
            if order_correct:
                print(f"\n🎉 第 {attempt + 1} 次尝试完全成功！")
                print("✅ 训练损失 < 验证损失")
                print("✅ K值性能顺序正确")
                
                # 保存结果
                torch.save(results, 'final_k_test_results.pth')
                print(f"\n💾 结果已保存")
                return True
            else:
                print(f"\n⚠️ K值性能顺序不正确")
        
        if attempt < max_attempts - 1:
            print(f"\n🔧 准备第 {attempt + 2} 次尝试...")
    
    print(f"\n❌ 经过 {max_attempts} 次尝试仍有问题")
    return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n需要进一步调试和修复")
