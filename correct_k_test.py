#!/usr/bin/env python3
"""
正确的K值测试脚本
期望：
1. 训练损失 < 验证损失
2. K=2性能 > K=1性能 > K=0性能 (即验证损失: K=2 < K=1 < K=0)
"""

import torch
import torch.nn as nn
import os
import glob
import numpy as np
from tqdm import tqdm
from gnn_model import GCRNPolicy
from training import Trainer, CoverageSequenceDataset

def create_test_dataset(processed_dataset_dir, k_value, num_samples=200):
    """创建测试数据集"""
    
    k_dir = os.path.join(processed_dataset_dir, f'k{k_value}')
    file_paths = sorted(glob.glob(os.path.join(k_dir, 'episode_*.pth')))
    
    print(f"K={k_value}: 找到{len(file_paths)}个文件")
    
    if len(file_paths) < num_samples:
        print(f"警告：只有{len(file_paths)}个文件，少于请求的{num_samples}个")
        num_samples = len(file_paths)
    
    selected_files = file_paths[:num_samples]
    print(f"K={k_value}: 加载{len(selected_files)}个episode")
    
    all_data = []
    for f_path in tqdm(selected_files, desc=f"Loading K={k_value}"):
        try:
            data = torch.load(f_path, map_location='cpu', weights_only=False)
            all_data.append((
                data['node_features'],
                data['adj_matrices'], 
                data['expert_actions']
            ))
        except Exception as e:
            print(f"加载失败 {f_path}: {e}")
            continue
    
    print(f"K={k_value}: 成功加载{len(all_data)}个序列")
    return CoverageSequenceDataset(all_data)

def test_single_k(k_value, num_samples=200, num_epochs=5):
    """测试单个K值"""
    
    print(f"\n{'='*50}")
    print(f"测试 K={k_value}")
    print(f"{'='*50}")
    
    # 创建数据集
    dataset = create_test_dataset('training_dataset_processed', k_value, num_samples)
    
    if len(dataset) == 0:
        print(f"K={k_value}数据集为空")
        return None
    
    # 分割数据
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    def seq_collate_fn(batch):
        return batch[0]
    
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=1, shuffle=True, collate_fn=seq_collate_fn
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=1, shuffle=False, collate_fn=seq_collate_fn
    )
    
    # 创建模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = GCRNPolicy(K_hops=k_value, hidden_dim=128).to(device)
    
    params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"模型参数: {params:,}")
    
    # 创建训练器
    trainer = Trainer(
        model, 
        learning_rate=0.001, 
        weight_decay=1e-4, 
        device=device, 
        direct_prediction=False,
        lambda_reg=0.001  # 适度的正则化
    )
    
    train_losses = []
    val_losses = []
    
    print(f"开始训练 {num_epochs} 轮...")
    
    for epoch in range(num_epochs):
        # 训练
        model.train()
        total_train_loss = 0
        train_count = 0
        
        for sequence_data in tqdm(train_loader, desc=f"Epoch {epoch+1} Train", leave=False):
            loss = trainer._process_sequence(sequence_data, is_training=True)
            total_train_loss += loss
            train_count += 1
        
        avg_train_loss = total_train_loss / train_count if train_count > 0 else float('inf')
        
        # 验证
        avg_val_loss = trainer._run_eval_loop(val_loader)
        
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        
        print(f"Epoch {epoch+1}: Train={avg_train_loss:.6f}, Val={avg_val_loss:.6f}")
        
        # 🔍 实时检查
        if avg_train_loss >= avg_val_loss:
            print(f"⚠️  异常：训练损失({avg_train_loss:.6f}) >= 验证损失({avg_val_loss:.6f})")
        else:
            print(f"✅ 正常：训练损失({avg_train_loss:.6f}) < 验证损失({avg_val_loss:.6f})")
    
    result = {
        'k_value': k_value,
        'params': params,
        'train_losses': train_losses,
        'val_losses': val_losses,
        'final_train': train_losses[-1] if train_losses else float('inf'),
        'final_val': val_losses[-1] if val_losses else float('inf')
    }
    
    return result

def analyze_results(results):
    """分析结果，验证期望"""
    
    print(f"\n{'='*60}")
    print("🔍 结果分析")
    print(f"{'='*60}")
    
    results = sorted(results, key=lambda x: x['k_value'])
    
    print("\n📊 参数数量:")
    for r in results:
        print(f"  K={r['k_value']}: {r['params']:,}")
    
    print(f"\n📈 最终损失:")
    for r in results:
        print(f"  K={r['k_value']}: Train={r['final_train']:.6f}, Val={r['final_val']:.6f}")
    
    # 🔍 关键检查1：训练损失 < 验证损失
    print(f"\n🎯 检查1：训练损失 < 验证损失")
    train_val_ok = True
    for r in results:
        k = r['k_value']
        train_loss = r['final_train']
        val_loss = r['final_val']
        
        if train_loss < val_loss:
            print(f"  ✅ K={k}: {train_loss:.6f} < {val_loss:.6f}")
        else:
            print(f"  ❌ K={k}: {train_loss:.6f} >= {val_loss:.6f} (异常)")
            train_val_ok = False
    
    # 🔍 关键检查2：K值性能顺序 (K=2 < K=1 < K=0 验证损失)
    print(f"\n🎯 检查2：K值性能顺序 (验证损失: K=2 < K=1 < K=0)")
    val_losses = [r['final_val'] for r in results]
    
    performance_ok = True
    if len(results) >= 2:
        # K=1 应该比 K=0 好 (损失更小)
        k0_val = results[0]['final_val']  # K=0
        k1_val = results[1]['final_val']  # K=1
        
        if k1_val < k0_val:
            improvement = (k0_val - k1_val) / k0_val * 100
            print(f"  ✅ K=1 < K=0: {k1_val:.6f} < {k0_val:.6f} (改进{improvement:.2f}%)")
        else:
            print(f"  ❌ K=1 >= K=0: {k1_val:.6f} >= {k0_val:.6f} (异常)")
            performance_ok = False
    
    if len(results) >= 3:
        # K=2 应该比 K=1 好 (损失更小)
        k1_val = results[1]['final_val']  # K=1
        k2_val = results[2]['final_val']  # K=2
        
        if k2_val < k1_val:
            improvement = (k1_val - k2_val) / k1_val * 100
            print(f"  ✅ K=2 < K=1: {k2_val:.6f} < {k1_val:.6f} (改进{improvement:.2f}%)")
        else:
            print(f"  ❌ K=2 >= K=1: {k2_val:.6f} >= {k1_val:.6f} (异常)")
            performance_ok = False
    
    # 总体评估
    print(f"\n🎯 总体评估:")
    if train_val_ok and performance_ok:
        print("🎉 所有检查通过！")
        print("✅ 训练损失 < 验证损失")
        print("✅ K值性能顺序正确")
        return True
    else:
        print("❌ 检测到问题：")
        if not train_val_ok:
            print("  - 训练损失 >= 验证损失 (异常)")
        if not performance_ok:
            print("  - K值性能顺序不正确")
        return False

def main():
    """主函数"""
    
    print("🚀 开始正确的K值测试")
    print("期望：")
    print("  1. 训练损失 < 验证损失")
    print("  2. K=2性能 > K=1性能 > K=0性能")
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 测试所有K值
    k_values = [0, 1, 2]
    results = []
    
    for k in k_values:
        try:
            result = test_single_k(k, num_samples=200, num_epochs=5)
            if result is not None:
                results.append(result)
        except Exception as e:
            print(f"❌ K={k}测试失败: {e}")
            continue
    
    if not results:
        print("❌ 所有测试都失败了")
        return
    
    # 分析结果
    success = analyze_results(results)
    
    # 保存结果
    torch.save(results, 'correct_k_test_results.pth')
    print(f"\n💾 结果已保存到 correct_k_test_results.pth")
    
    return success

if __name__ == "__main__":
    main()
