#!/usr/bin/env python3
"""
K=0问题的完整解决方案
根据用户期望：K=0 < K=1 < K=2，但提升递减
"""

import torch
import torch.nn as nn
import numpy as np

class TruelyUnifiedGCRNLayer(nn.Module):
    """真正统一的GCRN层，确保K=0只使用自身信息"""
    def __init__(self, input_dim, hidden_dim, dropout=0.1):
        super(TruelyUnifiedGCRNLayer, self).__init__()
        
        # 基础GRU组件（所有K值相同）
        self.reset_gate = nn.Sequential(
            nn.Linear(input_dim + hidden_dim, hidden_dim),
            nn.Sigmoid()
        )
        
        self.update_gate = nn.Sequential(
            nn.Linear(input_dim + hidden_dim, hidden_dim),
            nn.Sigmoid()
        )
        
        self.candidate = nn.Linear(input_dim + hidden_dim, hidden_dim)
        
        # 🔧 关键：图信息聚合层（只有K>0才真正使用）
        self.neighbor_transform = nn.Linear(hidden_dim, hidden_dim)
        self.neighbor_gate = nn.Sequential(
            nn.Linear(hidden_dim * 2, 1),
            nn.Sig<PERSON>id()
        )
        
        self.dropout = nn.Dropout(dropout)
        self.ln = nn.LayerNorm(hidden_dim)
        self.activation = nn.Tanh()
        
        # 统一初始化
        self._initialize_weights()
        
    def _initialize_weights(self):
        """统一的权重初始化"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, x_t, h_prev, adj_matrix):
        # 1. 基础GRU计算（所有K值完全相同）
        combined = torch.cat([x_t, h_prev], dim=1)
        
        reset = self.reset_gate(combined)
        update = self.update_gate(combined)
        
        combined_reset = torch.cat([x_t, reset * h_prev], dim=1)
        candidate_base = self.activation(self.candidate(combined_reset))
        
        # 2. 🔧 关键：真正的图信息处理
        # 检查是否为K=0（单位矩阵）
        is_identity = torch.allclose(adj_matrix, torch.eye(adj_matrix.shape[0], device=adj_matrix.device), atol=1e-6)
        
        if is_identity:
            # K=0：完全不使用图信息，只使用基础候选状态
            candidate_final = candidate_base
        else:
            # K>0：使用图信息聚合
            # 归一化邻接矩阵
            row_sums = adj_matrix.sum(dim=1, keepdim=True)
            adj_normalized = adj_matrix / (row_sums + 1e-8)
            
            # 邻居信息聚合
            neighbor_info = torch.matmul(adj_normalized, candidate_base)
            
            # 变换邻居信息
            neighbor_transformed = self.neighbor_transform(neighbor_info)
            
            # 计算邻居信息的权重
            gate_input = torch.cat([candidate_base, neighbor_transformed], dim=1)
            neighbor_weight = self.neighbor_gate(gate_input)
            
            # 融合自身信息和邻居信息
            candidate_final = candidate_base + neighbor_weight * neighbor_transformed
        
        # 3. 最终更新（所有K值相同）
        h_t = (1 - update) * h_prev + update * candidate_final
        h_t = self.ln(h_t)
        h_t = self.dropout(h_t)
        
        return h_t

class PhysicsBasedGCRNPolicy(nn.Module):
    """基于物理意义的GCRN策略，使用修正量学习"""
    def __init__(self, in_features=3, hidden_dim=128, out_features=2, K_hops=1, dropout=0.1):
        super(PhysicsBasedGCRNPolicy, self).__init__()
        self.K_hops = K_hops
        self.hidden_dim = hidden_dim
        
        # 特征编码器
        self.encoder = nn.Sequential(
            nn.Linear(in_features, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU()
        )
        
        # 统一的GCRN层
        self.gcrn = TruelyUnifiedGCRNLayer(hidden_dim, hidden_dim, dropout)
        
        # 修正量解码器（输出相对较小的修正量）
        self.decoder = nn.Sequential(
            nn.Linear(hidden_dim, 64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, out_features),
            nn.Tanh()  # 限制输出范围
        )
        
        self.hidden_state = None
        
    def reset(self, n_robots, device):
        self.hidden_state = torch.zeros(n_robots, self.hidden_dim, device=device)
        
    def forward(self, x_t, adj_matrix, h_prev=None):
        if h_prev is None:
            if self.hidden_state is None or self.hidden_state.shape[0] != x_t.shape[0]:
                self.reset(x_t.shape[0], x_t.device)
            h_prev = self.hidden_state
            
        # 编码输入
        x_encoded = self.encoder(x_t)
        
        # 应用GCRN层
        h_t = self.gcrn(x_encoded, h_prev, adj_matrix)
        
        # 保存隐藏状态
        self.hidden_state = h_t.detach()
        
        # 解码修正量
        delta_u = self.decoder(h_t)
        
        return delta_u, h_t

def test_k0_isolation():
    """测试K=0是否真正隔离"""
    
    print("=" * 60)
    print("测试K=0信息隔离")
    print("=" * 60)
    
    batch_size = 5
    feature_dim = 3
    hidden_dim = 64
    
    # 创建模型
    model_k0 = PhysicsBasedGCRNPolicy(K_hops=0, hidden_dim=hidden_dim, in_features=feature_dim)
    model_k1 = PhysicsBasedGCRNPolicy(K_hops=1, hidden_dim=hidden_dim, in_features=feature_dim)
    
    # 检查参数数量
    params_k0 = sum(p.numel() for p in model_k0.parameters())
    params_k1 = sum(p.numel() for p in model_k1.parameters())
    
    print(f"K=0参数数量: {params_k0:,}")
    print(f"K=1参数数量: {params_k1:,}")
    print(f"参数是否相同: {params_k0 == params_k1}")
    
    # 测试前向传播
    x_t = torch.randn(batch_size, feature_dim)
    
    # K=0邻接矩阵（单位矩阵）
    adj_k0 = torch.eye(batch_size)
    
    # K=1邻接矩阵（有邻居连接）
    adj_k1 = torch.rand(batch_size, batch_size)
    adj_k1 = (adj_k1 + adj_k1.T) / 2 + torch.eye(batch_size)
    
    # 前向传播
    model_k0.reset(batch_size, x_t.device)
    model_k1.reset(batch_size, x_t.device)
    
    output_k0, h_k0 = model_k0(x_t, adj_k0)
    output_k1, h_k1 = model_k1(x_t, adj_k1)
    
    print(f"\n前向传播测试:")
    print(f"K=0输出范围: [{output_k0.min():.4f}, {output_k0.max():.4f}]")
    print(f"K=1输出范围: [{output_k1.min():.4f}, {output_k1.max():.4f}]")
    
    # 测试K=0是否真正只使用自身信息
    # 修改其他机器人的特征，K=0的输出不应该改变
    x_t_modified = x_t.clone()
    x_t_modified[1:] = torch.randn_like(x_t_modified[1:])  # 修改除第一个机器人外的所有特征
    
    model_k0.reset(batch_size, x_t.device)
    output_k0_modified, _ = model_k0(x_t_modified, adj_k0)
    
    # K=0的第一个机器人输出应该相同（因为只使用自身信息）
    first_robot_diff = torch.abs(output_k0[0] - output_k0_modified[0]).max()
    print(f"\nK=0信息隔离测试:")
    print(f"第一个机器人输出差异: {first_robot_diff:.8f}")
    
    if first_robot_diff < 1e-6:
        print("✅ K=0真正只使用自身信息")
        return True
    else:
        print("❌ K=0仍在使用其他机器人信息")
        return False

def test_expected_performance_order():
    """测试期望的性能顺序：K=0 < K=1 < K=2"""
    
    print("\n" + "=" * 60)
    print("测试期望性能顺序")
    print("=" * 60)
    
    # 创建合成任务
    batch_size = 10
    feature_dim = 3
    hidden_dim = 64
    sequence_length = 20
    
    # 生成训练数据
    X = torch.randn(sequence_length, batch_size, feature_dim)
    
    # 🔧 关键：创建需要邻居信息的目标
    # 目标动作应该依赖于邻居信息
    y = torch.zeros(sequence_length, batch_size, 2)
    for t in range(sequence_length):
        for i in range(batch_size):
            # 自身贡献（局部Lloyd）
            local_contribution = X[t, i, :2] * X[t, i, 2]
            
            # 邻居贡献（需要邻居信息才能获得）
            neighbor_indices = [(i-1) % batch_size, (i+1) % batch_size]
            neighbor_contribution = torch.mean(X[t, neighbor_indices, :2], dim=0) * 0.3
            
            y[t, i] = local_contribution + neighbor_contribution
    
    # 创建模型
    models = {
        'K=0': PhysicsBasedGCRNPolicy(K_hops=0, hidden_dim=hidden_dim, in_features=feature_dim),
        'K=1': PhysicsBasedGCRNPolicy(K_hops=1, hidden_dim=hidden_dim, in_features=feature_dim),
        'K=2': PhysicsBasedGCRNPolicy(K_hops=2, hidden_dim=hidden_dim, in_features=feature_dim)
    }
    
    # 训练
    criterion = nn.MSELoss()
    epochs = 30
    
    final_losses = {}
    
    for k, model in models.items():
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        
        print(f"\n训练 {k} 模型...")
        
        for epoch in range(epochs):
            total_loss = 0
            model.reset(batch_size, X[0].device)
            
            for t in range(sequence_length):
                optimizer.zero_grad()
                
                # 创建对应的邻接矩阵
                if k == 'K=0':
                    adj = torch.eye(batch_size)
                elif k == 'K=1':
                    adj = torch.zeros(batch_size, batch_size)
                    for i in range(batch_size):
                        adj[i, i] = 1  # 自环
                        adj[i, (i-1) % batch_size] = 1  # 左邻居
                        adj[i, (i+1) % batch_size] = 1  # 右邻居
                else:  # K=2
                    adj = torch.zeros(batch_size, batch_size)
                    for i in range(batch_size):
                        for j in range(max(0, i-2), min(batch_size, i+3)):
                            adj[i, j] = 1
                
                # 前向传播
                delta_u, _ = model(X[t], adj)
                
                # 计算局部Lloyd基线
                local_lloyd = X[t, :, :2] * X[t, :, 2:3]
                
                # 预测总动作
                pred_action = local_lloyd + delta_u
                
                loss = criterion(pred_action, y[t])
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
            
            avg_loss = total_loss / sequence_length
            
            if (epoch + 1) % 10 == 0:
                print(f"  Epoch {epoch+1}: Loss = {avg_loss:.6f}")
        
        final_losses[k] = avg_loss
    
    # 分析结果
    print(f"\n最终损失:")
    for k in ['K=0', 'K=1', 'K=2']:
        print(f"  {k}: {final_losses[k]:.6f}")
    
    # 检查期望的性能顺序
    expected_order = final_losses['K=0'] > final_losses['K=1'] > final_losses['K=2']
    k1_improvement = (final_losses['K=0'] - final_losses['K=1']) / final_losses['K=0'] * 100
    k2_improvement = (final_losses['K=1'] - final_losses['K=2']) / final_losses['K=1'] * 100
    
    print(f"\n性能分析:")
    print(f"  K=1相对K=0改进: {k1_improvement:.2f}%")
    print(f"  K=2相对K=1改进: {k2_improvement:.2f}%")
    print(f"  期望顺序 (K=0 > K=1 > K=2): {expected_order}")
    
    return expected_order, final_losses

if __name__ == "__main__":
    print("开始K=0问题的完整修复测试...")
    
    # 测试1: K=0信息隔离
    isolation_success = test_k0_isolation()
    
    # 测试2: 期望性能顺序
    order_correct, losses = test_expected_performance_order()
    
    print(f"\n" + "=" * 60)
    print("修复验证总结")
    print("=" * 60)
    
    if isolation_success and order_correct:
        print("🎉 K=0问题完全修复！")
        print("   - K=0真正只使用自身信息")
        print("   - 性能顺序符合期望：K=0 < K=1 < K=2")
    else:
        print("⚠️  仍需进一步调整:")
        if not isolation_success:
            print("   - K=0信息隔离不完全")
        if not order_correct:
            print("   - 性能顺序不符合期望")
    
    print(f"\n建议的下一步:")
    print(f"1. 使用PhysicsBasedGCRNPolicy替换当前模型")
    print(f"2. 使用修正量学习而非直接预测")
    print(f"3. 重新训练并验证性能顺序")
