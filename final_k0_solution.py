#!/usr/bin/env python3
"""
K=0问题的最终解决方案
确保K=0真正只使用自身信息，实现期望的性能顺序
"""

import torch
import torch.nn as nn
import numpy as np

class IndependentGCRNLayer(nn.Module):
    """独立的GCRN层，确保K=0机器人之间完全独立"""
    def __init__(self, input_dim, hidden_dim, dropout=0.1, k_hops=1):
        super(IndependentGCRNLayer, self).__init__()
        
        self.k_hops = k_hops
        self.hidden_dim = hidden_dim

        # 基础GRU组件（所有K值相同）
        self.reset_gate = nn.Sequential(
            nn.Linear(input_dim + hidden_dim, hidden_dim),
            nn.Sigmoid()
        )

        self.update_gate = nn.Sequential(
            nn.Linear(input_dim + hidden_dim, hidden_dim),
            nn.Sigmoid()
        )

        self.candidate = nn.Linear(input_dim + hidden_dim, hidden_dim)

        # 邻居信息处理（只有K>0才使用）
        if k_hops > 0:
            self.neighbor_transform = nn.Linear(hidden_dim, hidden_dim)
            self.neighbor_gate = nn.Sequential(
                nn.Linear(hidden_dim * 2, 1),
                nn.Sigmoid()
            )
        else:
            # K=0创建相同的层但不使用，保持参数一致性
            self.neighbor_transform = nn.Linear(hidden_dim, hidden_dim)
            self.neighbor_gate = nn.Sequential(
                nn.Linear(hidden_dim * 2, 1),
                nn.Sigmoid()
            )

        self.dropout = nn.Dropout(dropout)
        self.ln = nn.LayerNorm(hidden_dim)
        self.activation = nn.Tanh()

        # 统一初始化
        self._initialize_weights()
        
    def _initialize_weights(self):
        """统一的权重初始化"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, x_t, h_prev, adj_matrix):
        """
        独立处理每个机器人
        x_t: [N, input_dim] - N个机器人的特征
        h_prev: [N, hidden_dim] - N个机器人的隐藏状态
        adj_matrix: [N, N] - 邻接矩阵
        """
        N = x_t.shape[0]
        h_new = torch.zeros_like(h_prev)
        
        # 🔧 关键：逐个处理每个机器人，确保K=0真正独立
        for i in range(N):
            # 当前机器人的特征和隐藏状态
            x_i = x_t[i:i+1]  # [1, input_dim]
            h_i = h_prev[i:i+1]  # [1, hidden_dim]
            
            # 基础GRU计算
            combined = torch.cat([x_i, h_i], dim=1)
            reset = self.reset_gate(combined)
            update = self.update_gate(combined)
            
            combined_reset = torch.cat([x_i, reset * h_i], dim=1)
            candidate_base = self.activation(self.candidate(combined_reset))
            
            if self.k_hops == 0:
                # K=0：完全不使用邻居信息
                candidate_final = candidate_base
            else:
                # K>0：使用邻居信息
                # 获取邻居索引
                neighbors = torch.nonzero(adj_matrix[i] > 0).squeeze(-1)
                
                if len(neighbors) > 0:
                    # 邻居的候选状态
                    neighbor_candidates = []
                    for j in neighbors:
                        x_j = x_t[j:j+1]
                        h_j = h_prev[j:j+1]
                        combined_j = torch.cat([x_j, h_j], dim=1)
                        reset_j = self.reset_gate(combined_j)
                        update_j = self.update_gate(combined_j)
                        combined_reset_j = torch.cat([x_j, reset_j * h_j], dim=1)
                        candidate_j = self.activation(self.candidate(combined_reset_j))
                        neighbor_candidates.append(candidate_j)
                    
                    # 聚合邻居信息
                    neighbor_weights = adj_matrix[i, neighbors].unsqueeze(-1)
                    neighbor_weights = neighbor_weights / neighbor_weights.sum()
                    
                    neighbor_info = torch.stack(neighbor_candidates, dim=0)
                    neighbor_aggregated = torch.sum(neighbor_info * neighbor_weights.unsqueeze(-1), dim=0)
                    
                    # 变换邻居信息
                    neighbor_transformed = self.neighbor_transform(neighbor_aggregated)
                    
                    # 计算邻居信息的权重
                    gate_input = torch.cat([candidate_base, neighbor_transformed], dim=1)
                    neighbor_weight = self.neighbor_gate(gate_input)
                    
                    # 融合自身信息和邻居信息
                    candidate_final = candidate_base + neighbor_weight * neighbor_transformed
                else:
                    # 没有邻居，只使用自身信息
                    candidate_final = candidate_base
            
            # 最终更新
            h_i_new = (1 - update) * h_i + update * candidate_final
            h_i_new = self.ln(h_i_new)
            h_i_new = self.dropout(h_i_new)
            
            h_new[i] = h_i_new.squeeze(0)
        
        return h_new

class TrulyIndependentGCRNPolicy(nn.Module):
    """真正独立的GCRN策略"""
    def __init__(self, in_features=3, hidden_dim=128, out_features=2, K_hops=1, dropout=0.1):
        super(TrulyIndependentGCRNPolicy, self).__init__()
        self.K_hops = K_hops
        self.hidden_dim = hidden_dim
        
        # 特征编码器
        self.encoder = nn.Sequential(
            nn.Linear(in_features, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU()
        )
        
        # 独立的GCRN层
        self.gcrn = IndependentGCRNLayer(hidden_dim, hidden_dim, dropout, K_hops)
        
        # 修正量解码器
        self.decoder = nn.Sequential(
            nn.Linear(hidden_dim, 64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, out_features),
            nn.Tanh()  # 限制输出范围
        )
        
        self.hidden_state = None
        
    def reset(self, n_robots, device):
        self.hidden_state = torch.zeros(n_robots, self.hidden_dim, device=device)
        
    def forward(self, x_t, adj_matrix, h_prev=None):
        if h_prev is None:
            if self.hidden_state is None or self.hidden_state.shape[0] != x_t.shape[0]:
                self.reset(x_t.shape[0], x_t.device)
            h_prev = self.hidden_state
            
        # 编码输入
        x_encoded = self.encoder(x_t)
        
        # 应用独立GCRN层
        h_t = self.gcrn(x_encoded, h_prev, adj_matrix)
        
        # 保存隐藏状态
        self.hidden_state = h_t.detach()
        
        # 解码修正量
        delta_u = self.decoder(h_t)
        
        return delta_u, h_t

def test_true_independence():
    """测试真正的独立性"""
    
    print("=" * 60)
    print("测试真正的K=0独立性")
    print("=" * 60)
    
    batch_size = 5
    feature_dim = 3
    hidden_dim = 64
    
    # 创建模型
    model_k0 = TrulyIndependentGCRNPolicy(K_hops=0, hidden_dim=hidden_dim, in_features=feature_dim)
    model_k1 = TrulyIndependentGCRNPolicy(K_hops=1, hidden_dim=hidden_dim, in_features=feature_dim)
    
    # 检查参数数量
    params_k0 = sum(p.numel() for p in model_k0.parameters())
    params_k1 = sum(p.numel() for p in model_k1.parameters())
    
    print(f"K=0参数数量: {params_k0:,}")
    print(f"K=1参数数量: {params_k1:,}")
    print(f"参数是否相同: {params_k0 == params_k1}")
    
    # 测试K=0独立性
    x_t = torch.randn(batch_size, feature_dim)
    adj_k0 = torch.eye(batch_size)
    
    # 第一次前向传播
    model_k0.reset(batch_size, x_t.device)
    output1, _ = model_k0(x_t, adj_k0)
    
    # 修改其他机器人特征
    x_t_modified = x_t.clone()
    x_t_modified[1:] = torch.randn_like(x_t_modified[1:])
    
    # 第二次前向传播
    model_k0.reset(batch_size, x_t.device)
    output2, _ = model_k0(x_t_modified, adj_k0)
    
    # 检查第一个机器人输出是否相同
    first_robot_diff = torch.abs(output1[0] - output2[0]).max()
    print(f"\nK=0独立性测试:")
    print(f"第一个机器人输出差异: {first_robot_diff:.8f}")
    
    if first_robot_diff < 1e-6:
        print("✅ K=0真正只使用自身信息")
        return True
    else:
        print("❌ K=0仍在使用其他机器人信息")
        return False

def test_performance_order():
    """测试期望的性能顺序"""
    
    print("\n" + "=" * 60)
    print("测试期望性能顺序")
    print("=" * 60)
    
    # 创建需要邻居信息的合成任务
    batch_size = 8
    feature_dim = 3
    hidden_dim = 64
    sequence_length = 15
    
    # 生成训练数据
    X = torch.randn(sequence_length, batch_size, feature_dim)
    
    # 创建需要邻居信息的目标
    y = torch.zeros(sequence_length, batch_size, 2)
    for t in range(sequence_length):
        for i in range(batch_size):
            # 自身贡献（局部信息）
            local_contribution = X[t, i, :2] * X[t, i, 2] * 0.7
            
            # 邻居贡献（需要邻居信息）
            left_neighbor = (i - 1) % batch_size
            right_neighbor = (i + 1) % batch_size
            neighbor_contribution = (X[t, left_neighbor, :2] + X[t, right_neighbor, :2]) * 0.15
            
            y[t, i] = local_contribution + neighbor_contribution
    
    # 创建模型
    models = {
        'K=0': TrulyIndependentGCRNPolicy(K_hops=0, hidden_dim=hidden_dim, in_features=feature_dim),
        'K=1': TrulyIndependentGCRNPolicy(K_hops=1, hidden_dim=hidden_dim, in_features=feature_dim),
        'K=2': TrulyIndependentGCRNPolicy(K_hops=2, hidden_dim=hidden_dim, in_features=feature_dim)
    }
    
    # 训练
    criterion = nn.MSELoss()
    epochs = 25
    
    final_losses = {}
    
    for k, model in models.items():
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        
        print(f"\n训练 {k} 模型...")
        
        for epoch in range(epochs):
            total_loss = 0
            model.reset(batch_size, X[0].device)
            
            for t in range(sequence_length):
                optimizer.zero_grad()
                
                # 创建对应的邻接矩阵
                if k == 'K=0':
                    adj = torch.eye(batch_size)
                elif k == 'K=1':
                    adj = torch.zeros(batch_size, batch_size)
                    for i in range(batch_size):
                        adj[i, i] = 1  # 自环
                        adj[i, (i-1) % batch_size] = 1  # 左邻居
                        adj[i, (i+1) % batch_size] = 1  # 右邻居
                else:  # K=2
                    adj = torch.zeros(batch_size, batch_size)
                    for i in range(batch_size):
                        for j in range(max(0, i-2), min(batch_size, i+3)):
                            adj[i, j] = 1
                
                # 前向传播
                delta_u, _ = model(X[t], adj)
                
                # 计算局部基线
                local_baseline = X[t, :, :2] * X[t, :, 2:3] * 0.7
                
                # 预测总动作
                pred_action = local_baseline + delta_u
                
                loss = criterion(pred_action, y[t])
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
            
            avg_loss = total_loss / sequence_length
            
            if (epoch + 1) % 5 == 0:
                print(f"  Epoch {epoch+1}: Loss = {avg_loss:.6f}")
        
        final_losses[k] = avg_loss
    
    # 分析结果
    print(f"\n最终损失:")
    for k in ['K=0', 'K=1', 'K=2']:
        print(f"  {k}: {final_losses[k]:.6f}")
    
    # 检查期望的性能顺序
    expected_order = final_losses['K=0'] > final_losses['K=1'] > final_losses['K=2']
    k1_improvement = (final_losses['K=0'] - final_losses['K=1']) / final_losses['K=0'] * 100
    k2_improvement = (final_losses['K=1'] - final_losses['K=2']) / final_losses['K=1'] * 100
    
    print(f"\n性能分析:")
    print(f"  K=1相对K=0改进: {k1_improvement:.2f}%")
    print(f"  K=2相对K=1改进: {k2_improvement:.2f}%")
    print(f"  期望顺序 (K=0 > K=1 > K=2): {expected_order}")
    
    return expected_order, final_losses

if __name__ == "__main__":
    print("开始K=0问题的最终解决方案测试...")
    
    # 测试1: 真正的独立性
    independence_success = test_true_independence()
    
    # 测试2: 期望性能顺序
    order_correct, losses = test_performance_order()
    
    print(f"\n" + "=" * 60)
    print("最终验证总结")
    print("=" * 60)
    
    if independence_success and order_correct:
        print("🎉 K=0问题完全解决！")
        print("   - K=0真正只使用自身信息")
        print("   - 性能顺序符合期望：K=0 < K=1 < K=2")
        print("   - 可以替换现有模型进行训练")
    else:
        print("⚠️  仍需进一步调整:")
        if not independence_success:
            print("   - K=0信息隔离不完全")
        if not order_correct:
            print("   - 性能顺序不符合期望")
    
    print(f"\n建议的实施步骤:")
    print(f"1. 用TrulyIndependentGCRNPolicy替换gnn_model.py中的GCRNPolicy")
    print(f"2. 确保使用修正量学习(direct_prediction=False)")
    print(f"3. 重新训练并验证结果")
