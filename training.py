# training.py (Enhanced Training Implementation)
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import os
import glob
import matplotlib.pyplot as plt
from torch.utils.data import Dataset, DataLoader
from tqdm import tqdm
from gnn_model import GCRNPolicy


class CoverageSequenceDataset(Dataset):
    def __init__(self, data_list): self.data = data_list

    def __len__(self): return len(self.data)

    def __getitem__(self, idx): return self.data[idx]


class Trainer:
    """ 增强版训练器，支持直接预测和修正量学习 """

    def __init__(self, model, learning_rate=1e-4, weight_decay=1e-4, device=None, 
                 direct_prediction=False, lambda_reg=0.1):
        self.device = device or torch.device('cpu')
        self.model = model.to(self.device)
        self.optimizer = optim.Adam(self.model.parameters(), lr=learning_rate, weight_decay=weight_decay)
        self.criterion = nn.MSELoss()
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(self.optimizer, 'min', factor=0.5, patience=10,
                                                              verbose=False)
        self.train_losses_for_plot = []
        self.val_losses = []
        self.direct_prediction = direct_prediction
        self.lambda_reg = lambda_reg  # 正则化系数

    def _process_sequence(self, sequence_data, is_training):
        node_seq, adj_seq, expert_action_seq = [d.to(self.device) for d in sequence_data]
        if is_training:
            self.optimizer.zero_grad()

        h_t = torch.zeros(node_seq.shape[1], self.model.hidden_dim, device=self.device)
        sequence_loss = 0

        for t in range(node_seq.shape[0]):
            node_features_t = node_seq[t]
            adj_matrix_t = adj_seq[t]
            expert_action_t = expert_action_seq[t]

            if self.direct_prediction:
                # 直接预测专家动作
                pred_action, h_t = self.model(node_features_t, adj_matrix_t, h_t)
                loss = self.criterion(pred_action, expert_action_t)
            else:
                # 🔧 改进的修正量学习
                # 计算局部Lloyd基线
                local_pos_diff = node_features_t[:, :2]
                local_mass = node_features_t[:, 2]
                u_local_t = 2 * local_mass.unsqueeze(1) * local_pos_diff

                # 预测修正量
                delta_u_t, h_t = self.model(node_features_t, adj_matrix_t, h_t)
                u_pred_t = u_local_t + delta_u_t

                # 计算真实修正量（用于更好的监督）
                u_true_correction = expert_action_t - u_local_t

                # 🔧 改进损失：直接监督修正量 + 总动作损失
                correction_loss = self.criterion(delta_u_t, u_true_correction)
                total_action_loss = self.criterion(u_pred_t, expert_action_t)
                reg_loss = self.lambda_reg * torch.mean(delta_u_t.pow(2))

                # 组合损失：修正量损失权重更高
                loss = 0.7 * correction_loss + 0.3 * total_action_loss + reg_loss
                
            sequence_loss += loss

        avg_loss = sequence_loss / node_seq.shape[0]

        if is_training:
            avg_loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
            self.optimizer.step()
            
        return avg_loss.item()

    def _run_eval_loop(self, data_loader):
        self.model.eval()
        total_loss = 0
        with torch.no_grad():
            for sequence_data in data_loader:
                total_loss += self._process_sequence(sequence_data, is_training=False)
        return total_loss / len(data_loader)

    def train(self, train_loader, val_loader, num_epochs, save_path, k_value, log_file_path):
        best_val_loss, best_epoch = float('inf'), 0
        with open(log_file_path, 'a', encoding='utf-8') as log_f:
            log_f.write(f"\n{'=' * 20} Training Log for K={k_value} {'=' * 20}\n")
            log_f.write(f"{'Epoch':<10}{'Train Loss':<20}{'Validation Loss':<20}\n{'-' * 10}{'-' * 20}{'-' * 20}\n")
            for epoch in range(num_epochs):
                self.model.train()
                train_pbar = tqdm(train_loader, desc=f"Epoch {epoch + 1}/{num_epochs} [Train K={k_value}]", leave=False)
                for sequence_data in train_pbar:
                    loss = self._process_sequence(sequence_data, is_training=True)
                    train_pbar.set_postfix({'batch_loss': f'{loss:.6f}'})

                avg_val_loss = self._run_eval_loop(val_loader)
                self.val_losses.append(avg_val_loss)
                avg_train_loss_for_plot = self._run_eval_loop(train_loader)
                self.train_losses_for_plot.append(avg_train_loss_for_plot)
                self.scheduler.step(avg_val_loss)

                log_f.write(f"{epoch + 1:<10}{avg_train_loss_for_plot:<20.6f}{avg_val_loss:<20.6f}\n")
                print(
                    f"Epoch {epoch + 1}: Train Loss (eval): {avg_train_loss_for_plot:.6f}, Val Loss: {avg_val_loss:.6f}")

                if avg_val_loss < best_val_loss:
                    best_val_loss, best_epoch = avg_val_loss, epoch + 1
                    torch.save(self.model.state_dict(), save_path)
                    print(f"  -> Saved new best model to {save_path}")

            summary_log = f"\nSummary for K={k_value}:\n  - Best Val Loss: {best_val_loss:.6f} at Epoch {best_epoch}\n{'=' * 52}\n"
            print(summary_log);
            log_f.write(summary_log)

    def plot_training_curves(self, save_path, k_value):
        plt.figure(figsize=(12, 7));
        plt.plot(self.train_losses_for_plot, label='Training Loss', color='royalblue')
        plt.plot(self.val_losses, label='Validation Loss', color='darkorange');
        plt.xlabel('Epoch');
        plt.ylabel('MSE Loss')
        plt.title(f'Training Progress (K={k_value})', fontsize=14, weight='bold');
        plt.grid(True, linestyle='--', alpha=0.6)
        plt.legend(loc='upper right');
        plt.tight_layout();
        plt.savefig(save_path, dpi=300);
        plt.close()
        print(f"Training progress plot saved to {save_path}")


def create_and_load_datasets(processed_dataset_dir, num_samples_to_use, k_value=None):
    if k_value is not None:
        # 从特定K值目录加载数据
        k_dir = os.path.join(processed_dataset_dir, f'k{k_value}')
        print(f"Loading pre-processed data for K={k_value} from '{k_dir}'...")
        file_paths = sorted(glob.glob(os.path.join(k_dir, 'episode_*.pth')))
        if not file_paths:
            raise FileNotFoundError(f"No processed .pth files found in {k_dir}")
    else:
        # 兼容旧版本：从根目录加载
        print(f"Loading pre-processed data from '{processed_dataset_dir}'...")
        file_paths = sorted(glob.glob(os.path.join(processed_dataset_dir, 'episode_*.pth')))
        if not file_paths:
            raise FileNotFoundError(f"No processed .pth files found in {processed_dataset_dir}")

    files_to_load = file_paths[:num_samples_to_use] if num_samples_to_use != -1 else file_paths
    print(f"Loading {len(files_to_load)} files...")
    all_episode_data = []
    for f_path in tqdm(files_to_load, desc="Loading Data"):
        data = torch.load(f_path, map_location='cpu')
        all_episode_data.append((data['node_features'], data['adj_matrices'], data['expert_actions']))
    return CoverageSequenceDataset(all_episode_data)
