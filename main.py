
# main.py (Enhanced Implementation)

import argparse
import os
import torch
import numpy as np
from tqdm import tqdm

from environment import Environment
from expert_controller import ExpertController, generate_clustered_initial_positions
from evaluation import CoverageEvaluator, plot_and_save_episode
from training import create_and_load_datasets, Trainer
from gnn_model import GCRNPolicy, LloydPolicy
from preprocess_data import preprocess_and_save


def generate_training_data(args):
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    os.makedirs(args.dataset_dir, exist_ok=True)

    print(f"Generating {args.n_episodes} expert episodes...")
    print(f"Episode Length: {args.episode_length} steps")
    print(f"Expert Control Gain: {args.control_gain}, Max Move Distance: {args.max_move_dist}")

    pbar = tqdm(range(args.n_episodes), desc="Generating Data")
    for trial in pbar:
        env = Environment(
            width=args.env_width, height=args.env_height, n_peaks=args.n_peaks, device=device
        )
        initial_pos = generate_clustered_initial_positions(
            n_robots=args.n_robots, env=env, cluster_std=args.cluster_std
        )
        expert = ExpertController(
            control_gain=args.control_gain, max_move_dist=args.max_move_dist
        )
        evaluator = CoverageEvaluator(env, args.sensing_radius, device)

        trajectory, rewards, _ = evaluator.run_episode(
            policy=expert, initial_pos=initial_pos, max_steps=args.episode_length
        )

        final_reward = rewards[-1]
        file_base_name = f'episode_{trial + 1:04d}'
        data_path = os.path.join(args.dataset_dir, f'{file_base_name}.pth')

        torch.save({
            'episode_id': trial + 1, 'trajectory': torch.stack(trajectory).cpu(), 'rewards': rewards,
            'peak_centers': env.peak_centers.cpu().numpy(),
            'peak_stds': env.stds_np, 'peak_amps': env.peak_amps_np
        }, data_path)

        title = f'Expert Episode {trial + 1}'
        plot_and_save_episode(
            trajectory, env, args.dataset_dir, file_base_name, title, final_reward=final_reward
        )

    print("\nDataset generation complete.")


def preprocess_data_main(args):
    preprocess_and_save(
        raw_dataset_dir=args.dataset_dir,
        processed_dataset_dir=args.processed_dataset_dir,
        sensing_radius=args.sensing_radius
    )


def train_models(args):
    full_dataset = create_and_load_datasets(
        processed_dataset_dir=args.processed_dataset_dir, num_samples_to_use=args.num_samples
    )
    val_size = len(full_dataset) // 5
    train_size = len(full_dataset) - val_size
    train_dataset, val_dataset = torch.utils.data.random_split(full_dataset, [train_size, val_size])

    def seq_collate_fn(batch):
        return batch[0]

    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=1, shuffle=True, collate_fn=seq_collate_fn
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=1, shuffle=False, collate_fn=seq_collate_fn
    )

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    log_file_path = "training_log.txt"
    if os.path.exists(log_file_path):
        os.remove(log_file_path)

    for k in args.k_hops_to_train:
        print(f"\n{'=' * 25} Starting Training for K={k} {'=' * 25}")
        model = GCRNPolicy(K_hops=k, hidden_dim=args.hidden_dim).to(device)
        print(
            f"Model: GCRN(K={k}), Hidden Dim: {args.hidden_dim}, Trainable Params: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

        trainer = Trainer(model, learning_rate=args.learning_rate, weight_decay=args.weight_decay, device=device, direct_prediction=True)
        save_path = f'gcrn_final_k{k}_best.pth'
        trainer.train(train_loader, val_loader, args.num_epochs, save_path, k_value=k, log_file_path=log_file_path)

        plot_path = f'training_progress_final_k{k}.png'
        trainer.plot_training_curves(plot_path, k_value=k)

    print(f"\nAll training sessions complete. See log at '{log_file_path}'.")


def main():
    parser = argparse.ArgumentParser(description='Final GCRN Offline Learning Pipeline')
    parser.add_argument('--mode', choices=['generate', 'preprocess', 'train', 'evaluate'], required=True)
    parser.add_argument('--dataset_dir', type=str, default='training_dataset_raw')
    parser.add_argument('--processed_dataset_dir', type=str, default='training_dataset_processed')
    parser.add_argument('--n_robots', type=int, default=10)
    parser.add_argument('--env_width', type=float, default=40.0)
    parser.add_argument('--env_height', type=float, default=40.0)
    parser.add_argument('--n_peaks', type=int, default=3)
    parser.add_argument('--sensing_radius', type=float, default=5.0)

    # --- Generate Mode Arguments ---
    parser.add_argument('--n_episodes', type=int, default=1000)
    parser.add_argument('--episode_length', type=int, default=150,
                        help="Fixed number of steps for expert data generation.")
    parser.add_argument('--cluster_std', type=float, default=5.0)
    parser.add_argument('--control_gain', type=float, default=0.3, help="Expert controller gain (K_prop).")
    parser.add_argument('--max_move_dist', type=float, default=0.5, help="Max distance expert can move per step.")

    # --- Train Mode Arguments ---
    parser.add_argument('--k_hops_to_train', type=int, nargs='+', default=[0, 1, 2])
    parser.add_argument('--num_samples', type=int, default=-1)
    parser.add_argument('--num_epochs', type=int, default=300)
    parser.add_argument('--learning_rate', type=float, default=0.0001)
    parser.add_argument('--hidden_dim', type=int, default=128)
    parser.add_argument('--weight_decay', type=float, default=1e-4)

    args = parser.parse_args()
    torch.manual_seed(42)
    np.random.seed(42)

    if args.mode == 'generate':
        generate_training_data(args)
    elif args.mode == 'preprocess':
        preprocess_data_main(args)
    elif args.mode == 'train':
        train_models(args)
    elif args.mode == 'evaluate':
        print("Evaluation logic needs to be finalized after a successful training run.")


if __name__ == "__main__":
    main()
