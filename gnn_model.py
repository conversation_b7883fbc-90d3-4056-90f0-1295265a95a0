
# gnn_model.py (Enhanced GCRN Implementation)

import torch
import torch.nn as nn
import numpy as np
from scipy.spatial import Delaunay
from environment import get_voronoi_metrics, lloyd_algorithm_step

class GATLayer(nn.Module):
    """优化的图注意力网络层"""
    def __init__(self, in_features, out_features, alpha=0.2, num_heads=4, dropout=0.1):
        super(GATLayer, self).__init__()
        self.in_features = in_features
        self.out_features = out_features
        self.alpha = alpha
        self.num_heads = num_heads
        self.head_dim = out_features // num_heads
        
        # 多头注意力
        self.W = nn.Linear(in_features, out_features, bias=False)
        self.attn = nn.Parameter(torch.zeros(size=(num_heads, 2 * self.head_dim, 1)))
        
        # 初始化参数
        nn.init.xavier_uniform_(self.W.weight)
        nn.init.xavier_uniform_(self.attn)
        
        self.leakyrelu = nn.LeakyReLU(self.alpha)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, h, adj):
        batch_size = h.size(0)
        
        # 线性变换
        Wh = self.W(h)  # [N, out_features]
        
        # 将特征分割为多头
        Wh = Wh.view(batch_size, self.num_heads, self.head_dim)  # [N, heads, head_dim]
        
        # 计算自注意力系数
        # 更高效的注意力计算
        a_input = torch.cat([
            Wh.repeat(1, batch_size, 1).view(batch_size * batch_size, self.num_heads, self.head_dim),
            Wh.repeat(batch_size, 1, 1)
        ], dim=2).view(batch_size, batch_size, self.num_heads, 2 * self.head_dim)
        
        # 转置以便于多头注意力计算
        a_input = a_input.permute(2, 0, 1, 3)  # [heads, N, N, 2*head_dim]
        
        # 计算注意力分数
        e = self.leakyrelu(torch.matmul(a_input, self.attn).squeeze(3))  # [heads, N, N]
        
        # 屏蔽不相连的节点
        zero_vec = -9e15 * torch.ones_like(e)
        attention = torch.where(adj.unsqueeze(0) > 0, e, zero_vec)  # [heads, N, N]
        
        # 归一化注意力权重
        attention = torch.nn.functional.softmax(attention, dim=2)  # [heads, N, N]
        attention = self.dropout(attention)
        
        # 应用注意力权重
        h_prime = torch.matmul(attention, Wh.permute(1, 0, 2))  # [heads, N, head_dim]
        h_prime = h_prime.permute(1, 0, 2).contiguous().view(batch_size, -1)  # [N, out_features]
        
        return torch.nn.functional.elu(h_prime)

class GCRNLayer(nn.Module):
    """增强版GCRN层，添加完整的GRU门控机制"""
    def __init__(self, input_dim, hidden_dim, alpha=0.2, dropout=0.1, num_heads=4):
        super(GCRNLayer, self).__init__()
        # 输入处理
        self.gat_x = GATLayer(input_dim, hidden_dim, alpha, num_heads, dropout)
        
        # 重置门
        self.reset_gate = nn.Sequential(
            GATLayer(input_dim + hidden_dim, hidden_dim, alpha, num_heads, dropout),
            nn.Sigmoid()
        )
        
        # 更新门
        self.update_gate = nn.Sequential(
            GATLayer(input_dim + hidden_dim, hidden_dim, alpha, num_heads, dropout),
            nn.Sigmoid()
        )
        
        # 候选隐藏状态
        self.candidate = GATLayer(input_dim + hidden_dim, hidden_dim, alpha, num_heads, dropout)
        
        self.dropout = nn.Dropout(dropout)
        self.ln = nn.LayerNorm(hidden_dim)
        self.activation = nn.Tanh()

    def forward(self, x_t, h_prev, adj_matrix):
        # 处理输入
        x_processed = self.gat_x(x_t, adj_matrix)
        
        # 拼接特征
        combined = torch.cat([x_t, h_prev], dim=1)
        
        # 计算门控值
        reset = self.reset_gate(combined, adj_matrix)
        update = self.update_gate(combined, adj_matrix)
        
        # 计算候选隐藏状态
        combined_reset = torch.cat([x_t, reset * h_prev], dim=1)
        candidate = self.activation(self.candidate(combined_reset, adj_matrix))
        
        # 更新隐藏状态
        h_t = (1 - update) * h_prev + update * candidate
        h_t = self.ln(h_t)
        h_t = self.dropout(h_t)
        
        return h_t

class SimplifiedGCRNLayer(nn.Module):
    """简化版GCRN层，用于K=0情况，保持架构一致性"""
    def __init__(self, input_dim, hidden_dim, dropout=0.1):
        super(SimplifiedGCRNLayer, self).__init__()
        # 重置门
        self.reset_gate = nn.Sequential(
            nn.Linear(input_dim + hidden_dim, hidden_dim),
            nn.Sigmoid()
        )
        
        # 更新门
        self.update_gate = nn.Sequential(
            nn.Linear(input_dim + hidden_dim, hidden_dim),
            nn.Sigmoid()
        )
        
        # 候选隐藏状态
        self.candidate = nn.Linear(input_dim + hidden_dim, hidden_dim)
        
        self.dropout = nn.Dropout(dropout)
        self.ln = nn.LayerNorm(hidden_dim)
        self.activation = nn.Tanh()
        
    def forward(self, x_t, h_prev, adj_matrix=None):
        # 拼接特征
        combined = torch.cat([x_t, h_prev], dim=1)
        
        # 计算门控值
        reset = self.reset_gate(combined)
        update = self.update_gate(combined)
        
        # 计算候选隐藏状态
        combined_reset = torch.cat([x_t, reset * h_prev], dim=1)
        candidate = self.activation(self.candidate(combined_reset))
        
        # 更新隐藏状态
        h_t = (1 - update) * h_prev + update * candidate
        h_t = self.ln(h_t)
        h_t = self.dropout(h_t)
        
        return h_t

class GCRNPolicy(nn.Module):
    """统一架构的GCRN策略"""
    def __init__(self, in_features=3, hidden_dim=128, out_features=2, K_hops=1, alpha=0.2, dropout=0.1, num_heads=4):
        super(GCRNPolicy, self).__init__()
        self.K_hops = K_hops
        self.hidden_dim = hidden_dim
        
        # 特征编码器
        self.encoder = nn.Sequential(
            nn.Linear(in_features, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU()
        )
        
        # 对K=0使用简化的GCRN (无图结构)
        if K_hops == 0:
            self.gcrn = SimplifiedGCRNLayer(hidden_dim, hidden_dim, dropout)
        else:
            self.gcrn = GCRNLayer(hidden_dim, hidden_dim, alpha, dropout, num_heads)
        
        # 解码器
        self.decoder = nn.Sequential(
            nn.Linear(hidden_dim, 64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, out_features)
        )
        
        self.hidden_state = None
        
    def reset(self, n_robots, device):
        self.hidden_state = torch.zeros(n_robots, self.hidden_dim, device=device)
        
    def forward(self, x_t, adj_matrix, h_prev=None):
        if h_prev is None:
            if self.hidden_state is None or self.hidden_state.shape[0] != x_t.shape[0]:
                self.reset(x_t.shape[0], x_t.device)
            h_prev = self.hidden_state
            
        # 编码输入
        x_encoded = self.encoder(x_t)
        
        # 应用GCRN层
        h_t = self.gcrn(x_encoded, h_prev, adj_matrix)
        
        # 保存隐藏状态
        self.hidden_state = h_t.detach()
        
        # 解码输出
        delta_u = self.decoder(h_t)
        
        return delta_u, h_t

def build_delaunay_graph(robot_pos):
    device = robot_pos.device; N = robot_pos.shape[0]
    if N < 3: return torch.eye(N, device=device)
    try:
        tri = Delaunay(robot_pos.detach().cpu().numpy())
        adj = torch.zeros((N, N), dtype=torch.float32, device=device)
        for s in tri.simplices:
            for i in range(3):
                for j in range(i + 1, 3):
                    adj[s[i], s[j]] = adj[s[j], s[i]] = 1.0
    except Exception:
        adj = torch.ones((N, N), dtype=torch.float32, device=device)
    return adj + torch.eye(N, device=device)

def build_weighted_delaunay_graph(robot_pos):
    """构建加权Delaunay图，边权重基于机器人间距离"""
    device = robot_pos.device
    N = robot_pos.shape[0]
    
    if N < 3:
        return torch.eye(N, device=device)
    
    try:
        # 构建Delaunay图的拓扑结构
        tri = Delaunay(robot_pos.detach().cpu().numpy())
        adj = torch.zeros((N, N), dtype=torch.float32, device=device)
        
        # 计算所有机器人对之间的距离
        distances = torch.cdist(robot_pos, robot_pos)
        
        # 归一化常数C
        C = 1.0 / distances.max()
        
        # 为Delaunay边赋予权重
        for s in tri.simplices:
            for i in range(3):
                for j in range(i + 1, 3):
                    # 边权重 = C * 距离
                    weight = C * distances[s[i], s[j]]
                    adj[s[i], s[j]] = adj[s[j], s[i]] = weight
    except Exception:
        # 失败时使用完全连接图
        distances = torch.cdist(robot_pos, robot_pos)
        C = 1.0 / distances.max()
        adj = C * distances
        
    # 添加自环 (对角线为1)
    adj = adj + torch.eye(N, device=device)
    
    return adj

def build_multiscale_graph(robot_pos, scales=[1.0, 2.0, 4.0]):
    """构建多尺度图，捕获不同范围的交互"""
    device = robot_pos.device
    N = robot_pos.shape[0]
    
    # 基础Delaunay图
    base_adj = build_delaunay_graph(robot_pos)
    
    # 距离矩阵
    distances = torch.cdist(robot_pos, robot_pos)
    max_dist = distances.max()
    
    # 多尺度邻接矩阵
    multi_adj = base_adj.clone()
    
    # 添加不同尺度的连接
    for scale in scales:
        radius = max_dist / scale
        scale_adj = (distances <= radius).float()
        multi_adj = multi_adj + scale_adj
    
    # 归一化并确保值在[0,1]范围内
    multi_adj = torch.clamp(multi_adj / (len(scales) + 1), 0, 1)
    
    # 添加自环
    multi_adj = multi_adj + torch.eye(N, device=device)
    
    return multi_adj

def prepare_node_features(robot_pos, env, sensing_radius, use_multiscale=False, normalize_params=None):
    """准备节点特征，支持多尺度图和特征归一化"""
    # 获取Voronoi指标
    masses, centroids = get_voronoi_metrics(robot_pos, env, sensing_radius)
    
    # 计算位置偏移 (p_i - c_i)
    position_diff = robot_pos.to(centroids.device) - centroids
    
    # 构建节点特征 [位置偏移x, 位置偏移y, 质量]
    node_features = torch.cat([position_diff, masses.unsqueeze(1)], dim=1)
    
    # 应用特征归一化
    if normalize_params is not None:
        mean = normalize_params['mean'].to(node_features.device)
        std = normalize_params['std'].to(node_features.device)
        node_features = (node_features - mean) / (std + 1e-8)
    
    # 构建图结构
    if use_multiscale:
        adj_matrix = build_multiscale_graph(robot_pos)
    else:
        adj_matrix = build_weighted_delaunay_graph(robot_pos)
    
    return node_features, adj_matrix

class LloydPolicy:
    def __init__(self, sensing_radius):
        self.sensing_radius = sensing_radius
    def __call__(self, robot_pos, env):
        return lloyd_algorithm_step(robot_pos, env, self.sensing_radius, infinite_sensing=False)
