
# gnn_model.py (Enhanced GCRN Implementation)

import torch
import torch.nn as nn
import numpy as np
from scipy.spatial import Delaunay
from environment import get_voronoi_metrics, lloyd_algorithm_step

class GATLayer(nn.Module):
    """优化的图注意力网络层"""
    def __init__(self, in_features, out_features, alpha=0.2, num_heads=4, dropout=0.1):
        super(GATLayer, self).__init__()
        self.in_features = in_features
        self.out_features = out_features
        self.alpha = alpha
        self.num_heads = num_heads
        self.head_dim = out_features // num_heads
        
        # 多头注意力
        self.W = nn.Linear(in_features, out_features, bias=False)
        self.attn = nn.Parameter(torch.zeros(size=(num_heads, 2 * self.head_dim, 1)))
        
        # 初始化参数
        nn.init.xavier_uniform_(self.W.weight)
        nn.init.xavier_uniform_(self.attn)
        
        self.leakyrelu = nn.LeakyReLU(self.alpha)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, h, adj):
        batch_size = h.size(0)

        # 线性变换
        Wh = self.W(h)  # [N, out_features]

        # 将特征分割为多头
        Wh = Wh.view(batch_size, self.num_heads, self.head_dim)  # [N, heads, head_dim]

        # 简化的注意力计算
        # 为每个头计算注意力
        attention_heads = []
        for head in range(self.num_heads):
            # 获取当前头的特征
            Wh_head = Wh[:, head, :]  # [N, head_dim]

            # 计算注意力系数
            # 创建所有节点对的特征拼接
            Wh_i = Wh_head.unsqueeze(1).repeat(1, batch_size, 1)  # [N, N, head_dim]
            Wh_j = Wh_head.unsqueeze(0).repeat(batch_size, 1, 1)  # [N, N, head_dim]

            # 拼接特征
            concat_features = torch.cat([Wh_i, Wh_j], dim=2)  # [N, N, 2*head_dim]

            # 计算注意力分数
            e = self.leakyrelu(torch.matmul(concat_features, self.attn[head]).squeeze(2))  # [N, N]

            # 屏蔽不相连的节点
            zero_vec = -9e15 * torch.ones_like(e)
            attention = torch.where(adj > 0, e, zero_vec)  # [N, N]

            # 归一化注意力权重
            attention = torch.nn.functional.softmax(attention, dim=1)  # [N, N]
            attention = self.dropout(attention)

            attention_heads.append(attention)

        # 应用注意力权重并聚合多头结果
        h_prime_heads = []
        for head in range(self.num_heads):
            h_prime_head = torch.matmul(attention_heads[head], Wh[:, head, :])  # [N, head_dim]
            h_prime_heads.append(h_prime_head)

        # 拼接所有头的结果
        h_prime = torch.cat(h_prime_heads, dim=1)  # [N, out_features]

        return torch.nn.functional.elu(h_prime)

class GCRNLayer(nn.Module):
    """增强版GCRN层，添加完整的GRU门控机制"""
    def __init__(self, input_dim, hidden_dim, alpha=0.2, dropout=0.1, num_heads=4):
        super(GCRNLayer, self).__init__()
        # 输入处理
        self.gat_x = GATLayer(input_dim, hidden_dim, alpha, num_heads, dropout)

        # 重置门
        self.reset_gate_gat = GATLayer(input_dim + hidden_dim, hidden_dim, alpha, num_heads, dropout)
        self.reset_gate_sigmoid = nn.Sigmoid()

        # 更新门
        self.update_gate_gat = GATLayer(input_dim + hidden_dim, hidden_dim, alpha, num_heads, dropout)
        self.update_gate_sigmoid = nn.Sigmoid()

        # 候选隐藏状态
        self.candidate = GATLayer(input_dim + hidden_dim, hidden_dim, alpha, num_heads, dropout)

        self.dropout = nn.Dropout(dropout)
        self.ln = nn.LayerNorm(hidden_dim)
        self.activation = nn.Tanh()

    def forward(self, x_t, h_prev, adj_matrix):
        # 处理输入
        x_processed = self.gat_x(x_t, adj_matrix)

        # 拼接特征
        combined = torch.cat([x_t, h_prev], dim=1)

        # 计算门控值
        reset = self.reset_gate_sigmoid(self.reset_gate_gat(combined, adj_matrix))
        update = self.update_gate_sigmoid(self.update_gate_gat(combined, adj_matrix))

        # 计算候选隐藏状态
        combined_reset = torch.cat([x_t, reset * h_prev], dim=1)
        candidate = self.activation(self.candidate(combined_reset, adj_matrix))

        # 更新隐藏状态
        h_t = (1 - update) * h_prev + update * candidate
        h_t = self.ln(h_t)
        h_t = self.dropout(h_t)

        return h_t

class SimplifiedGCRNLayer(nn.Module):
    """简化版GCRN层，用于K=0情况，保持架构一致性"""
    def __init__(self, input_dim, hidden_dim, dropout=0.1):
        super(SimplifiedGCRNLayer, self).__init__()
        # 重置门
        self.reset_gate = nn.Sequential(
            nn.Linear(input_dim + hidden_dim, hidden_dim),
            nn.Sigmoid()
        )
        
        # 更新门
        self.update_gate = nn.Sequential(
            nn.Linear(input_dim + hidden_dim, hidden_dim),
            nn.Sigmoid()
        )
        
        # 候选隐藏状态
        self.candidate = nn.Linear(input_dim + hidden_dim, hidden_dim)
        
        self.dropout = nn.Dropout(dropout)
        self.ln = nn.LayerNorm(hidden_dim)
        self.activation = nn.Tanh()
        
    def forward(self, x_t, h_prev, adj_matrix=None):
        # 拼接特征
        combined = torch.cat([x_t, h_prev], dim=1)
        
        # 计算门控值
        reset = self.reset_gate(combined)
        update = self.update_gate(combined)
        
        # 计算候选隐藏状态
        combined_reset = torch.cat([x_t, reset * h_prev], dim=1)
        candidate = self.activation(self.candidate(combined_reset))
        
        # 更新隐藏状态
        h_t = (1 - update) * h_prev + update * candidate
        h_t = self.ln(h_t)
        h_t = self.dropout(h_t)
        
        return h_t

class GCRNPolicy(nn.Module):
    """统一架构的GCRN策略"""
    def __init__(self, in_features=3, hidden_dim=128, out_features=2, K_hops=1, alpha=0.2, dropout=0.1, num_heads=4):
        super(GCRNPolicy, self).__init__()
        self.K_hops = K_hops
        self.hidden_dim = hidden_dim
        
        # 特征编码器
        self.encoder = nn.Sequential(
            nn.Linear(in_features, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU()
        )
        
        # 对K=0使用简化的GCRN (无图结构)
        if K_hops == 0:
            self.gcrn = SimplifiedGCRNLayer(hidden_dim, hidden_dim, dropout)
        else:
            self.gcrn = GCRNLayer(hidden_dim, hidden_dim, alpha, dropout, num_heads)
        
        # 解码器
        self.decoder = nn.Sequential(
            nn.Linear(hidden_dim, 64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, out_features)
        )
        
        self.hidden_state = None
        
    def reset(self, n_robots, device):
        self.hidden_state = torch.zeros(n_robots, self.hidden_dim, device=device)
        
    def forward(self, x_t, adj_matrix, h_prev=None):
        if h_prev is None:
            if self.hidden_state is None or self.hidden_state.shape[0] != x_t.shape[0]:
                self.reset(x_t.shape[0], x_t.device)
            h_prev = self.hidden_state
            
        # 编码输入
        x_encoded = self.encoder(x_t)
        
        # 应用GCRN层
        h_t = self.gcrn(x_encoded, h_prev, adj_matrix)
        
        # 保存隐藏状态
        self.hidden_state = h_t.detach()
        
        # 解码输出
        delta_u = self.decoder(h_t)
        
        return delta_u, h_t

def build_delaunay_graph(robot_pos):
    device = robot_pos.device; N = robot_pos.shape[0]
    if N < 3: return torch.eye(N, device=device)
    try:
        tri = Delaunay(robot_pos.detach().cpu().numpy())
        adj = torch.zeros((N, N), dtype=torch.float32, device=device)
        for s in tri.simplices:
            for i in range(3):
                for j in range(i + 1, 3):
                    adj[s[i], s[j]] = adj[s[j], s[i]] = 1.0
    except Exception:
        adj = torch.ones((N, N), dtype=torch.float32, device=device)
    return adj + torch.eye(N, device=device)

def build_weighted_delaunay_graph(robot_pos):
    """构建加权Delaunay图，边权重基于机器人间距离"""
    device = robot_pos.device
    N = robot_pos.shape[0]
    
    if N < 3:
        return torch.eye(N, device=device)
    
    try:
        # 构建Delaunay图的拓扑结构
        tri = Delaunay(robot_pos.detach().cpu().numpy())
        adj = torch.zeros((N, N), dtype=torch.float32, device=device)
        
        # 计算所有机器人对之间的距离
        distances = torch.cdist(robot_pos, robot_pos)
        
        # 归一化常数C
        C = 1.0 / distances.max()
        
        # 为Delaunay边赋予权重
        for s in tri.simplices:
            for i in range(3):
                for j in range(i + 1, 3):
                    # 边权重 = C * 距离
                    weight = C * distances[s[i], s[j]]
                    adj[s[i], s[j]] = adj[s[j], s[i]] = weight
    except Exception:
        # 失败时使用完全连接图
        distances = torch.cdist(robot_pos, robot_pos)
        C = 1.0 / distances.max()
        adj = C * distances
        
    # 添加自环 (对角线为1)
    adj = adj + torch.eye(N, device=device)
    
    return adj

def build_multiscale_graph(robot_pos, scales=[1.0, 2.0, 4.0]):
    """构建多尺度图，捕获不同范围的交互"""
    device = robot_pos.device
    N = robot_pos.shape[0]
    
    # 基础Delaunay图
    base_adj = build_delaunay_graph(robot_pos)
    
    # 距离矩阵
    distances = torch.cdist(robot_pos, robot_pos)
    max_dist = distances.max()
    
    # 多尺度邻接矩阵
    multi_adj = base_adj.clone()
    
    # 添加不同尺度的连接
    for scale in scales:
        radius = max_dist / scale
        scale_adj = (distances <= radius).float()
        multi_adj = multi_adj + scale_adj
    
    # 归一化并确保值在[0,1]范围内
    multi_adj = torch.clamp(multi_adj / (len(scales) + 1), 0, 1)
    
    # 添加自环
    multi_adj = multi_adj + torch.eye(N, device=device)
    
    return multi_adj

def build_k_hop_graph(robot_pos, k_hops, sensing_radius=None):
    """构建K-hop邻接矩阵，正确实现图的K跳连接"""
    device = robot_pos.device
    N = robot_pos.shape[0]

    if k_hops == 0:
        # K=0: 只有自环
        return torch.eye(N, device=device, dtype=torch.float32)

    # 基础图：基于距离的邻接矩阵（不包含自环）
    distances = torch.cdist(robot_pos, robot_pos)

    if sensing_radius is not None:
        # 基于感知半径的连接（排除自环）
        base_adj = (distances <= sensing_radius).float()
        base_adj = base_adj - torch.eye(N, device=device)  # 移除自环
    else:
        # 基于Delaunay三角剖分的连接（排除自环）
        base_adj = build_delaunay_graph(robot_pos) - torch.eye(N, device=device)

    if k_hops == 1:
        # K=1: 直接邻居 + 自环
        return base_adj + torch.eye(N, device=device)

    # K>1: 计算K-hop可达性
    # 使用邻接矩阵的幂来计算K跳可达性
    adj_power = base_adj.clone()
    k_hop_reachable = base_adj.clone()

    for _ in range(1, k_hops):
        # 计算下一跳
        adj_power = torch.matmul(adj_power, base_adj)
        # 累加可达性（任何路径长度 <= k_hops）
        k_hop_reachable = k_hop_reachable + adj_power

    # 二值化：有连接为1，无连接为0
    k_hop_adj = (k_hop_reachable > 0).float()

    # 添加自环
    k_hop_adj = k_hop_adj + torch.eye(N, device=device)

    return k_hop_adj

def prepare_node_features(robot_pos, env, sensing_radius, use_multiscale=False, normalize_params=None, k_hops=None):
    """准备节点特征，支持多尺度图和特征归一化，正确实现K-hop邻居"""
    # 获取Voronoi指标
    masses, centroids = get_voronoi_metrics(robot_pos, env, sensing_radius)

    # 计算位置偏移 (p_i - c_i)
    position_diff = robot_pos.to(centroids.device) - centroids

    # 构建节点特征 [位置偏移x, 位置偏移y, 质量]
    node_features = torch.cat([position_diff, masses.unsqueeze(1)], dim=1)

    # 应用特征归一化
    if normalize_params is not None:
        mean = normalize_params['mean'].to(node_features.device)
        std = normalize_params['std'].to(node_features.device)
        node_features = (node_features - mean) / (std + 1e-8)

    # 构建图结构
    if k_hops is not None:
        # 使用K-hop图构建
        adj_matrix = build_k_hop_graph(robot_pos, k_hops, sensing_radius)
    else:
        # 兼容旧版本：使用默认图构建
        if use_multiscale:
            adj_matrix = build_multiscale_graph(robot_pos)
        else:
            adj_matrix = build_weighted_delaunay_graph(robot_pos)

    return node_features, adj_matrix

class LloydPolicy:
    def __init__(self, sensing_radius):
        self.sensing_radius = sensing_radius
    def __call__(self, robot_pos, env):
        return lloyd_algorithm_step(robot_pos, env, self.sensing_radius, infinite_sensing=False)
