#!/usr/bin/env python3
"""
诊断训练问题的脚本
重点检查：
1. 训练损失 vs 验证损失
2. K值数据量一致性
3. 损失计算逻辑
"""

import torch
import torch.nn as nn
import os
import glob
import numpy as np
from gnn_model import GCRNPolicy
from training import Trainer, CoverageSequenceDataset

def diagnose_data_consistency():
    """诊断数据一致性问题"""
    
    print("🔍 诊断数据一致性...")
    
    for k in [0, 1, 2]:
        k_dir = os.path.join('training_dataset_processed', f'k{k}')
        file_paths = sorted(glob.glob(os.path.join(k_dir, 'episode_*.pth')))
        
        print(f"\nK={k}:")
        print(f"  文件数量: {len(file_paths)}")
        
        if len(file_paths) > 0:
            # 检查第一个文件的结构
            try:
                data = torch.load(file_paths[0], map_location='cpu', weights_only=False)
                node_features = data['node_features']
                adj_matrices = data['adj_matrices']
                expert_actions = data['expert_actions']
                
                print(f"  序列长度: {node_features.shape[0]}")
                print(f"  节点数量: {node_features.shape[1]}")
                print(f"  特征维度: {node_features.shape[2]}")
                print(f"  动作维度: {expert_actions.shape}")
                
            except Exception as e:
                print(f"  ❌ 加载失败: {e}")

def diagnose_loss_calculation():
    """诊断损失计算逻辑"""
    
    print("\n🔍 诊断损失计算逻辑...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建小型测试数据
    batch_size = 5
    seq_len = 10
    node_count = 20
    feature_dim = 3  # 🔧 修复：使用正确的特征维度

    # 模拟数据
    node_features = torch.randn(seq_len, node_count, feature_dim).to(device)
    adj_matrices = torch.randn(seq_len, node_count, node_count).to(device)
    expert_actions = torch.randn(seq_len, node_count, 2).to(device)
    
    sequence_data = (node_features, adj_matrices, expert_actions)
    
    for k in [0, 1, 2]:
        print(f"\n测试 K={k}:")
        
        model = GCRNPolicy(K_hops=k, hidden_dim=64).to(device)
        trainer = Trainer(
            model, 
            learning_rate=0.001, 
            device=device, 
            direct_prediction=False,
            lambda_reg=0.0001
        )
        
        # 测试训练模式损失
        model.train()
        train_loss = trainer._process_sequence(sequence_data, is_training=False)  # 不更新参数
        
        # 测试验证模式损失
        model.eval()
        with torch.no_grad():
            val_loss = trainer._process_sequence(sequence_data, is_training=False)
        
        print(f"  训练模式损失: {train_loss:.6f}")
        print(f"  验证模式损失: {val_loss:.6f}")
        print(f"  差异: {abs(train_loss - val_loss):.6f}")
        
        if abs(train_loss - val_loss) < 1e-6:
            print("  ✅ 损失计算一致")
        else:
            print("  ❌ 损失计算不一致！")

def diagnose_regularization_effect():
    """诊断正则化效果"""
    
    print("\n🔍 诊断正则化效果...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建测试数据
    seq_len = 5
    node_count = 10
    feature_dim = 3  # 🔧 修复：使用正确的特征维度
    
    node_features = torch.randn(seq_len, node_count, feature_dim).to(device)
    adj_matrices = torch.randn(seq_len, node_count, node_count).to(device)
    expert_actions = torch.randn(seq_len, node_count, 2).to(device)
    
    sequence_data = (node_features, adj_matrices, expert_actions)
    
    model = GCRNPolicy(K_hops=1, hidden_dim=64).to(device)
    
    # 测试不同正则化强度
    reg_values = [0.0, 0.0001, 0.001, 0.01]
    
    for reg in reg_values:
        trainer = Trainer(
            model, 
            learning_rate=0.001, 
            device=device, 
            direct_prediction=False,
            lambda_reg=reg
        )
        
        model.eval()
        with torch.no_grad():
            loss = trainer._process_sequence(sequence_data, is_training=False)
        
        print(f"  正则化={reg}: 损失={loss:.6f}")

def quick_training_test():
    """快速训练测试，验证修复效果"""
    
    print("\n🚀 快速训练测试...")
    
    # 只测试K=1，验证修复效果
    try:
        from quick_test_k_values import create_quick_test_dataset
        
        dataset = create_quick_test_dataset('training_dataset_processed', 1, 50)  # 只用50个样本
        
        if len(dataset) == 0:
            print("❌ 数据集为空")
            return
        
        # 分割数据
        train_size = int(0.8 * len(dataset))
        val_size = len(dataset) - train_size
        train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
        
        def seq_collate_fn(batch):
            return batch[0]
        
        train_loader = torch.utils.data.DataLoader(
            train_dataset, batch_size=1, shuffle=True, collate_fn=seq_collate_fn
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset, batch_size=1, shuffle=False, collate_fn=seq_collate_fn
        )
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = GCRNPolicy(K_hops=1, hidden_dim=64).to(device)
        
        trainer = Trainer(
            model, 
            learning_rate=0.001, 
            device=device, 
            direct_prediction=False,
            lambda_reg=0.0001
        )
        
        print("训练1轮进行验证...")
        
        # 训练1轮
        model.train()
        total_train_loss = 0
        train_count = 0
        
        for sequence_data in train_loader:
            loss = trainer._process_sequence(sequence_data, is_training=True)
            total_train_loss += loss
            train_count += 1
            
            if train_count >= 10:  # 只训练10个batch
                break
        
        avg_train_loss = total_train_loss / train_count
        
        # 验证
        model.eval()
        total_val_loss = 0
        val_count = 0
        
        with torch.no_grad():
            for sequence_data in val_loader:
                loss = trainer._process_sequence(sequence_data, is_training=False)
                total_val_loss += loss
                val_count += 1
                
                if val_count >= 5:  # 只验证5个batch
                    break
        
        avg_val_loss = total_val_loss / val_count
        
        print(f"训练损失: {avg_train_loss:.6f}")
        print(f"验证损失: {avg_val_loss:.6f}")
        
        if avg_train_loss >= avg_val_loss:
            print("✅ 训练损失 >= 验证损失，修复成功！")
        else:
            print("❌ 训练损失 < 验证损失，仍有问题")
            
    except Exception as e:
        print(f"❌ 快速测试失败: {e}")

def main():
    """主诊断函数"""
    
    print("🔧 开始诊断训练问题...")
    
    # 1. 诊断数据一致性
    diagnose_data_consistency()
    
    # 2. 诊断损失计算逻辑
    diagnose_loss_calculation()
    
    # 3. 诊断正则化效果
    diagnose_regularization_effect()
    
    # 4. 快速训练测试
    quick_training_test()
    
    print("\n🎯 诊断完成！")

if __name__ == "__main__":
    main()
