# preprocess_data.py (Enhanced Preprocessing Implementation)

import argparse
import os
import torch
import numpy as np
import glob
from tqdm import tqdm

from environment import Environment
from gnn_model import prepare_node_features


def preprocess_and_save(raw_dataset_dir, processed_dataset_dir, sensing_radius, use_multiscale=False, apply_augmentation=True, k_hops_list=[0, 1, 2], base_dt=0.05, adaptive_dt=False):
    """
    增强版数据预处理函数，支持特征归一化、数据增强和动态时间步长，为不同K值生成对应数据
    """
    print("=" * 60)
    print("Starting Enhanced Data Preprocessing...")
    print(f"Source: '{raw_dataset_dir}' -> Destination: '{processed_dataset_dir}'")
    print(f"Using multiscale graph: {use_multiscale}, Applying augmentation: {apply_augmentation}")
    print(f"Processing for K values: {k_hops_list}")
    print(f"Base time step: {base_dt}, Adaptive time step: {adaptive_dt}")
    print("=" * 60)

    # 为每个K值创建子目录
    for k in k_hops_list:
        k_dir = os.path.join(processed_dataset_dir, f'k{k}')
        os.makedirs(k_dir, exist_ok=True)

    file_paths = sorted(glob.glob(os.path.join(raw_dataset_dir, 'episode_*.pth')))
    if not file_paths:
        raise FileNotFoundError(f"No raw .pth files found in directory: '{raw_dataset_dir}'.")

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # 优化方案：单次处理，同时计算所有K值的统计信息和数据
    print("\nOptimized single-pass processing...")

    # 为所有K值收集特征统计信息
    all_features_by_k = {k: [] for k in k_hops_list}

    # 第一遍：收集统计信息
    print("Pass 1: Computing feature statistics for all K values...")
    for f_path in tqdm(file_paths, desc="Computing Statistics"):
        data = torch.load(f_path, map_location='cpu', weights_only=False)
        trajectory = data['trajectory']

        env = Environment(
            peak_centers=data['peak_centers'],
            peak_std=data['peak_stds'],
            peak_amps=data['peak_amps'],
            device=device
        )

        for t in range(trajectory.shape[0] - 1):
            robot_pos_t = trajectory[t].to(device)

            # 为每个K值计算特征
            for k in k_hops_list:
                node_features, _ = prepare_node_features(robot_pos_t, env, sensing_radius,
                                                       use_multiscale=use_multiscale, k_hops=k)
                all_features_by_k[k].append(node_features.cpu())

    # 计算并保存每个K值的归一化参数
    norm_params_by_k = {}
    for k in k_hops_list:
        all_features = torch.cat(all_features_by_k[k], dim=0)
        feature_mean = all_features.mean(dim=0)
        feature_std = all_features.std(dim=0)

        norm_params = {
            'mean': feature_mean,
            'std': feature_std
        }
        norm_params_by_k[k] = norm_params

        k_dir = os.path.join(processed_dataset_dir, f'k{k}')
        torch.save(norm_params, os.path.join(k_dir, 'normalization_params.pth'))
        print(f"K={k} normalization parameters saved: mean={feature_mean}, std={feature_std}")

    # 清理内存
    del all_features_by_k

    # 第二遍：处理数据并保存
    print("\nPass 2: Processing and saving data for all K values...")
    for f_path in tqdm(file_paths, desc="Processing Episodes"):
        data = torch.load(f_path, map_location='cpu', weights_only=False)
        trajectory = data['trajectory']

        env = Environment(
            peak_centers=data['peak_centers'],
            peak_std=data['peak_stds'],
            peak_amps=data['peak_amps'],
            device=device
        )

        # 动态时间步长处理（对所有K值相同）
        if adaptive_dt:
            # 计算每个时间步的自适应dt
            expert_actions = []
            for t in range(trajectory.shape[0] - 1):
                position_diff = trajectory[t+1] - trajectory[t]
                velocities = position_diff / base_dt  # 基于基础dt的速度
                max_vel = torch.max(torch.norm(velocities, dim=1))

                # 自适应时间步长
                if max_vel > 1.0:  # 最大速度限制
                    adaptive_dt_val = base_dt * (1.0 / max_vel.item())
                else:
                    adaptive_dt_val = base_dt
                adaptive_dt_val = max(0.01, min(0.1, adaptive_dt_val))

                # 计算动作
                action = position_diff / adaptive_dt_val
                expert_actions.append(action)
            expert_actions = torch.stack(expert_actions).to(device)
        else:
            # 固定时间步长
            expert_actions = ((trajectory[1:] - trajectory[:-1]) / base_dt).to(device)

        # 🔧 修复：为所有K值使用相同的数据增强
        # 首先决定是否应用增强和增强参数（对所有K值相同）
        apply_augmentation_this_file = apply_augmentation and np.random.rand() < 0.5
        if apply_augmentation_this_file:
            # 为所有K值使用相同的随机旋转角度
            theta = np.random.uniform(0, 2*np.pi)
            rot_matrix = torch.tensor([
                [np.cos(theta), -np.sin(theta)],
                [np.sin(theta), np.cos(theta)]
            ], dtype=torch.float32, device=device)

        # 为每个K值处理当前文件
        for k in k_hops_list:
            k_dir = os.path.join(processed_dataset_dir, f'k{k}')
            norm_params = norm_params_by_k[k]

            node_features_seq, adj_matrices_seq = [], []
            for t in range(trajectory.shape[0] - 1):
                robot_pos_t = trajectory[t].to(device)
                node_features, adj_matrix = prepare_node_features(
                    robot_pos_t, env, sensing_radius,
                    use_multiscale=use_multiscale,
                    normalize_params=norm_params,
                    k_hops=k  # 传递K值
                )
                node_features_seq.append(node_features)
                adj_matrices_seq.append(adj_matrix)

            if not node_features_seq:
                continue

            # 复制专家动作用于增强
            current_expert_actions = expert_actions.clone()

            # 应用相同的数据增强（如果决定增强的话）
            if apply_augmentation_this_file:
                # 应用预先确定的旋转到位置差异特征和专家动作
                for i in range(len(node_features_seq)):
                    # 旋转位置差异 (前两维)
                    pos_diff = node_features_seq[i][:, :2]
                    node_features_seq[i][:, :2] = torch.matmul(pos_diff, rot_matrix.T)

                    # 旋转专家动作
                    current_expert_actions[i] = torch.matmul(current_expert_actions[i], rot_matrix.T)

            # 🔧 新增：计算局部Lloyd基线和修正量
            local_lloyd_actions = []
            correction_actions = []

            for i in range(len(node_features_seq)):
                features = node_features_seq[i]
                # 局部Lloyd动作：2 * mass * position_diff
                local_action = 2 * features[:, 2:3] * features[:, :2]
                local_lloyd_actions.append(local_action)

                # 修正量：expert_action - local_lloyd_action
                correction = current_expert_actions[i] - local_action
                correction_actions.append(correction)

            processed_data = {
                'node_features': torch.stack(node_features_seq).cpu(),
                'adj_matrices': torch.stack(adj_matrices_seq).cpu(),
                'expert_actions': current_expert_actions.cpu(),
                'local_lloyd_actions': torch.stack(local_lloyd_actions).cpu(),
                'correction_actions': torch.stack(correction_actions).cpu()
            }

            save_path = os.path.join(k_dir, os.path.basename(f_path))
            torch.save(processed_data, save_path)

    print("\nPreprocessing complete.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Preprocess raw trajectory data for GCRN training.')
    parser.add_argument('--raw_dir', type=str, default='training_dataset_raw')
    parser.add_argument('--processed_dir', type=str, default='training_dataset_processed')
    parser.add_argument('--sensing_radius', type=float, default=5.0)
    parser.add_argument('--use_multiscale', action='store_true', help='Use multiscale graph construction')
    parser.add_argument('--apply_augmentation', action='store_true', help='Apply data augmentation')
    args = parser.parse_args()
    preprocess_and_save(args.raw_dir, args.processed_dir, args.sensing_radius, 
                        args.use_multiscale, args.apply_augmentation)
