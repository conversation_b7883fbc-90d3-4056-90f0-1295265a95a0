#!/usr/bin/env python3
"""
测试统一架构的效果
"""

import torch
import torch.nn as nn
import numpy as np
from gnn_model import GCRNPolicy

def test_unified_architecture():
    """测试统一架构是否解决了K=0问题"""
    
    print("=" * 60)
    print("测试统一架构")
    print("=" * 60)
    
    # 创建模型
    model_k0 = GCRNPolicy(K_hops=0, hidden_dim=128)
    model_k1 = GCRNPolicy(K_hops=1, hidden_dim=128)
    model_k2 = GCRNPolicy(K_hops=2, hidden_dim=128)
    
    # 检查参数数量
    def count_parameters(model):
        return sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    params_k0 = count_parameters(model_k0)
    params_k1 = count_parameters(model_k1)
    params_k2 = count_parameters(model_k2)
    
    print(f"K=0模型参数: {params_k0:,}")
    print(f"K=1模型参数: {params_k1:,}")
    print(f"K=2模型参数: {params_k2:,}")
    
    # 检查是否参数数量相同
    if params_k0 == params_k1 == params_k2:
        print("✅ 所有K值模型参数数量相同")
    else:
        print("❌ 参数数量不同")
        return False
    
    # 检查架构类型
    print(f"\nK=0 GCRN层类型: {type(model_k0.gcrn).__name__}")
    print(f"K=1 GCRN层类型: {type(model_k1.gcrn).__name__}")
    print(f"K=2 GCRN层类型: {type(model_k2.gcrn).__name__}")
    
    # 测试前向传播
    batch_size = 5
    feature_dim = 3
    
    # 输入数据
    x_t = torch.randn(batch_size, feature_dim)
    
    # 不同的邻接矩阵
    adj_k0 = torch.eye(batch_size)  # K=0: 只有自环
    adj_k1 = torch.rand(batch_size, batch_size)  # K=1: 有邻居连接
    adj_k1 = (adj_k1 + adj_k1.T) / 2 + torch.eye(batch_size)  # 对称化
    adj_k2 = torch.ones(batch_size, batch_size)  # K=2: 全连接
    
    # 前向传播
    model_k0.reset(batch_size, x_t.device)
    model_k1.reset(batch_size, x_t.device)
    model_k2.reset(batch_size, x_t.device)
    
    output_k0, _ = model_k0(x_t, adj_k0)
    output_k1, _ = model_k1(x_t, adj_k1)
    output_k2, _ = model_k2(x_t, adj_k2)
    
    print(f"\n前向传播测试:")
    print(f"K=0输出形状: {output_k0.shape}, 均值: {output_k0.mean():.4f}")
    print(f"K=1输出形状: {output_k1.shape}, 均值: {output_k1.mean():.4f}")
    print(f"K=2输出形状: {output_k2.shape}, 均值: {output_k2.mean():.4f}")
    
    return True

def test_learning_fairness():
    """测试学习公平性"""
    
    print("\n" + "=" * 60)
    print("测试学习公平性")
    print("=" * 60)
    
    # 创建简单的学习任务
    batch_size = 10
    feature_dim = 3
    hidden_dim = 64
    sequence_length = 20
    
    # 生成训练数据
    X = torch.randn(sequence_length, batch_size, feature_dim)
    y = torch.randn(sequence_length, batch_size, 2)  # 目标输出
    
    # 创建模型
    models = {
        'K=0': GCRNPolicy(K_hops=0, hidden_dim=hidden_dim, in_features=feature_dim),
        'K=1': GCRNPolicy(K_hops=1, hidden_dim=hidden_dim, in_features=feature_dim),
        'K=2': GCRNPolicy(K_hops=2, hidden_dim=hidden_dim, in_features=feature_dim)
    }
    
    # 优化器
    optimizers = {}
    for k, model in models.items():
        optimizers[k] = torch.optim.Adam(model.parameters(), lr=0.001)
    
    criterion = nn.MSELoss()
    
    # 训练
    epochs = 20
    losses = {k: [] for k in models.keys()}
    
    print("开始训练...")
    for epoch in range(epochs):
        epoch_losses = {}
        
        for k, model in models.items():
            model.train()
            optimizer = optimizers[k]
            
            total_loss = 0
            model.reset(batch_size, X[0].device)
            
            for t in range(sequence_length):
                optimizer.zero_grad()
                
                # 创建对应的邻接矩阵
                if k == 'K=0':
                    adj = torch.eye(batch_size)
                elif k == 'K=1':
                    adj = torch.rand(batch_size, batch_size)
                    adj = (adj + adj.T) / 2 + torch.eye(batch_size)
                else:  # K=2
                    adj = torch.ones(batch_size, batch_size) * 0.5 + torch.eye(batch_size) * 0.5
                
                # 前向传播
                pred, _ = model(X[t], adj)
                loss = criterion(pred, y[t])
                
                loss.backward()
                optimizer.step()
                total_loss += loss.item()
            
            avg_loss = total_loss / sequence_length
            losses[k].append(avg_loss)
            epoch_losses[k] = avg_loss
        
        if (epoch + 1) % 5 == 0:
            print(f"Epoch {epoch+1}: " + 
                  ", ".join([f"{k}={v:.6f}" for k, v in epoch_losses.items()]))
    
    # 最终结果
    print(f"\n最终损失:")
    for k in models.keys():
        print(f"  {k}: {losses[k][-1]:.6f}")
    
    # 检查是否更公平
    final_losses = [losses[k][-1] for k in models.keys()]
    loss_std = np.std(final_losses)
    print(f"\n损失标准差: {loss_std:.6f}")
    
    if loss_std < 0.01:
        print("✅ 学习结果相对公平")
        return True
    else:
        print("⚠️  仍存在学习差异")
        return False

def main():
    """主测试函数"""
    
    print("开始测试统一架构解决方案...")
    
    # 测试1: 架构统一性
    arch_test = test_unified_architecture()
    
    # 测试2: 学习公平性
    fair_test = test_learning_fairness()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if arch_test and fair_test:
        print("🎉 统一架构解决方案成功！")
        print("   - 所有K值使用相同的参数数量")
        print("   - 学习过程更加公平")
        print("   - 可以进行真正的K值比较")
    else:
        print("❌ 仍需要进一步调整")
    
    print(f"\n下一步：")
    print(f"1. 重新训练所有K值模型")
    print(f"2. 比较训练结果")
    print(f"3. 验证K=0不再异常优秀")

if __name__ == "__main__":
    main()
