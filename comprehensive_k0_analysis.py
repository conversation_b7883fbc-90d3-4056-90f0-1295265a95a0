#!/usr/bin/env python3
"""
K=0问题的全面分析和解决方案
"""

import torch
import torch.nn as nn
import numpy as np
from gnn_model import GCRNPolicy, SimplifiedGCRNLayer, GCRNLayer

def analyze_model_architectures():
    """分析不同K值模型架构的差异"""
    
    print("=" * 60)
    print("模型架构深度分析")
    print("=" * 60)
    
    # 创建模型
    model_k0 = GCRNPolicy(K_hops=0, hidden_dim=128)
    model_k1 = GCRNPolicy(K_hops=1, hidden_dim=128)
    
    # 分析参数数量
    def count_parameters(model):
        total = sum(p.numel() for p in model.parameters())
        trainable = sum(p.numel() for p in model.parameters() if p.requires_grad)
        return total, trainable
    
    total_k0, trainable_k0 = count_parameters(model_k0)
    total_k1, trainable_k1 = count_parameters(model_k1)
    
    print(f"K=0模型: 总参数={total_k0:,}, 可训练={trainable_k0:,}")
    print(f"K=1模型: 总参数={total_k1:,}, 可训练={trainable_k1:,}")
    print(f"参数差异: {trainable_k1 - trainable_k0:,}")
    
    # 分析层结构
    print(f"\nK=0 GCRN层: {type(model_k0.gcrn).__name__}")
    print(f"K=1 GCRN层: {type(model_k1.gcrn).__name__}")
    
    # 详细分析SimplifiedGCRNLayer
    print(f"\nSimplifiedGCRNLayer结构:")
    for name, module in model_k0.gcrn.named_modules():
        if name:  # 跳过根模块
            print(f"  {name}: {module}")
    
    # 详细分析GCRNLayer
    print(f"\nGCRNLayer结构:")
    for name, module in model_k1.gcrn.named_modules():
        if name and not name.startswith('gat'):  # 简化输出
            print(f"  {name}: {module}")
    
    return model_k0, model_k1

def test_forward_pass_differences():
    """测试前向传播的差异"""
    
    print("\n" + "=" * 60)
    print("前向传播差异分析")
    print("=" * 60)
    
    # 创建测试数据
    batch_size = 5
    feature_dim = 3
    hidden_dim = 128
    
    # 输入数据
    x_t = torch.randn(batch_size, feature_dim)
    h_prev = torch.randn(batch_size, hidden_dim)
    adj_k0 = torch.eye(batch_size)  # K=0: 只有自环
    adj_k1 = torch.rand(batch_size, batch_size)  # K=1: 有邻居连接
    adj_k1 = (adj_k1 + adj_k1.T) / 2 + torch.eye(batch_size)  # 对称化并加自环
    
    # 创建层
    simplified_layer = SimplifiedGCRNLayer(feature_dim, hidden_dim)
    gcrn_layer = GCRNLayer(feature_dim, hidden_dim)
    
    # 前向传播
    print("测试前向传播...")
    
    # SimplifiedGCRNLayer (K=0)
    h_out_k0 = simplified_layer(x_t, h_prev, adj_k0)
    print(f"K=0输出形状: {h_out_k0.shape}")
    print(f"K=0输出统计: 均值={h_out_k0.mean():.4f}, 标准差={h_out_k0.std():.4f}")
    
    # GCRNLayer (K=1)
    h_out_k1 = gcrn_layer(x_t, h_prev, adj_k1)
    print(f"K=1输出形状: {h_out_k1.shape}")
    print(f"K=1输出统计: 均值={h_out_k1.mean():.4f}, 标准差={h_out_k1.std():.4f}")
    
    # 比较输出差异
    if h_out_k0.shape == h_out_k1.shape:
        diff = torch.abs(h_out_k0 - h_out_k1).mean()
        print(f"输出差异: {diff:.6f}")
    
    return h_out_k0, h_out_k1

def analyze_learning_capacity():
    """分析学习能力差异"""
    
    print("\n" + "=" * 60)
    print("学习能力分析")
    print("=" * 60)
    
    # 创建简单的学习任务
    batch_size = 10
    feature_dim = 3
    hidden_dim = 64
    
    # 生成训练数据
    X = torch.randn(100, batch_size, feature_dim)
    y = torch.randn(100, batch_size, 2)  # 目标输出
    
    # 创建模型
    model_k0 = GCRNPolicy(K_hops=0, hidden_dim=hidden_dim, in_features=feature_dim)
    model_k1 = GCRNPolicy(K_hops=1, hidden_dim=hidden_dim, in_features=feature_dim)
    
    # 优化器
    optimizer_k0 = torch.optim.Adam(model_k0.parameters(), lr=0.001)
    optimizer_k1 = torch.optim.Adam(model_k1.parameters(), lr=0.001)
    
    criterion = nn.MSELoss()
    
    # 训练几个epoch
    epochs = 10
    losses_k0 = []
    losses_k1 = []
    
    for epoch in range(epochs):
        # K=0训练
        model_k0.train()
        total_loss_k0 = 0
        for i in range(len(X)):
            optimizer_k0.zero_grad()
            
            # 重置隐藏状态
            model_k0.reset(batch_size, X[i].device)
            
            # 前向传播
            adj_k0 = torch.eye(batch_size)
            pred_k0, _ = model_k0(X[i], adj_k0)
            loss_k0 = criterion(pred_k0, y[i])
            
            loss_k0.backward()
            optimizer_k0.step()
            total_loss_k0 += loss_k0.item()
        
        # K=1训练
        model_k1.train()
        total_loss_k1 = 0
        for i in range(len(X)):
            optimizer_k1.zero_grad()
            
            # 重置隐藏状态
            model_k1.reset(batch_size, X[i].device)
            
            # 前向传播
            adj_k1 = torch.rand(batch_size, batch_size)
            adj_k1 = (adj_k1 + adj_k1.T) / 2 + torch.eye(batch_size)
            pred_k1, _ = model_k1(X[i], adj_k1)
            loss_k1 = criterion(pred_k1, y[i])
            
            loss_k1.backward()
            optimizer_k1.step()
            total_loss_k1 += loss_k1.item()
        
        avg_loss_k0 = total_loss_k0 / len(X)
        avg_loss_k1 = total_loss_k1 / len(X)
        
        losses_k0.append(avg_loss_k0)
        losses_k1.append(avg_loss_k1)
        
        print(f"Epoch {epoch+1}: K=0 Loss={avg_loss_k0:.6f}, K=1 Loss={avg_loss_k1:.6f}")
    
    print(f"\n最终损失: K=0={losses_k0[-1]:.6f}, K=1={losses_k1[-1]:.6f}")
    
    return losses_k0, losses_k1

def identify_root_cause():
    """识别根本原因"""
    
    print("\n" + "=" * 60)
    print("根本原因分析")
    print("=" * 60)
    
    potential_causes = [
        "1. 架构复杂度不匹配：SimplifiedGCRNLayer vs GCRNLayer",
        "2. 参数初始化差异：不同层的初始化策略",
        "3. 梯度流动差异：MLP vs GAT的梯度传播",
        "4. 正则化效应：简单架构的隐式正则化",
        "5. 任务适配性：覆盖控制任务可能更适合简单架构",
        "6. 过拟合风险：复杂模型更容易过拟合小数据集",
        "7. 训练稳定性：GAT层的训练可能不稳定"
    ]
    
    for cause in potential_causes:
        print(f"  {cause}")
    
    print(f"\n🎯 最可能的原因：")
    print(f"  SimplifiedGCRNLayer使用简单的线性层，训练更稳定")
    print(f"  GCRNLayer使用GAT层，可能存在梯度消失或训练不稳定问题")
    print(f"  覆盖控制任务可能不需要复杂的图注意力机制")

def propose_solutions():
    """提出解决方案"""
    
    print("\n" + "=" * 60)
    print("解决方案")
    print("=" * 60)
    
    solutions = [
        "方案1: 统一架构 - 让所有K值都使用相同的基础架构",
        "方案2: 调整学习率 - 为复杂模型使用更小的学习率",
        "方案3: 增强正则化 - 为简单模型增加更强的正则化",
        "方案4: 修改GAT层 - 简化GAT层的实现，提高训练稳定性",
        "方案5: 预训练策略 - 先训练简单版本，再迁移到复杂版本",
        "方案6: 集成方法 - 结合不同K值的预测结果"
    ]
    
    for i, solution in enumerate(solutions, 1):
        print(f"  {solution}")
    
    print(f"\n🔧 推荐方案：统一架构")
    print(f"  让K=0也使用GCRNLayer，但在前向传播时忽略邻接矩阵")
    print(f"  这样可以确保所有K值使用相同的参数数量和架构复杂度")

if __name__ == "__main__":
    # 运行分析
    print("开始K=0问题的全面分析...")
    
    # 1. 架构分析
    model_k0, model_k1 = analyze_model_architectures()
    
    # 2. 前向传播分析
    h_out_k0, h_out_k1 = test_forward_pass_differences()
    
    # 3. 学习能力分析
    losses_k0, losses_k1 = analyze_learning_capacity()
    
    # 4. 根本原因识别
    identify_root_cause()
    
    # 5. 解决方案
    propose_solutions()
    
    print(f"\n" + "=" * 60)
    print("分析完成")
    print("=" * 60)
    print(f"主要发现：SimplifiedGCRNLayer比GCRNLayer训练更稳定")
    print(f"建议：统一使用相同的架构，通过邻接矩阵控制信息传播")
